// 主应用

// 1. 【qiankun】注册微应用
// 外部写法

registerMicroApp(
  [
    {
      name: "microApp1",
      entry: "https://xxx.com",
      container: "#micro-app",
      activeRule: "/micro-app1",
    },
  ],
  {
    beforeLoad: (app) => {
      console.log("before load", app.name);
      return Promise.resolve();
    },
    beforeMount: (app) => {
      console.log("before mount", app.name);
      return Promise.resolve();
    },
    afterMount: (app) => {
      console.log("after mount", app.name);
      return Promise.resolve();
    },
    beforeUnmount: (app) => {
      console.log("before unmount", app.name);
      return Promise.resolve();
    },
    afterUnmount: (app) => {
      console.log("after unmount", app.name);
      return Promise.resolve();
    },
  }
);


// 内部实现

export function registerMicroApps(apps, lifeCycles) {
  var unregisteredApps = apps.filter(function (app) {
    return !microApps.some(function (registeredApp) {
      return registeredApp.name === app.name;
    });
  });
  microApps = [].concat(_toConsumableArray(microApps), _toConsumableArray(unregisteredApps));
  unregisteredApps.forEach(function (app) {
    var name = app.name,
      activeRule = app.activeRule,
      _app$loader = app.loader,
      loader = _app$loader === void 0 ? _noop : _app$loader,
      props = app.props,
      appConfig = _objectWithoutProperties(app, _excluded);

    // 下面的注册函数是single-spa的入口函数，这个库专门用于处理微应用的生命周期
    registerApplication({
      name: name,
      app: function () {
        var _app = _asyncToGenerator(_regeneratorRuntime.mark(function _callee3() {
          var _yield$loadApp, mount, otherMicroAppConfigs;
          return _regeneratorRuntime.wrap(function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                loader(true);
                _context3.next = 3;
                return frameworkStartedDefer.promise;

              // 3.1 第一步：加载微应用
              case 3:
                _context3.next = 5;

                // loadApp的内在逻辑是：
                return loadApp(_objectSpread({
                  name: name,
                  props: props
                }, appConfig), frameworkConfiguration, lifeCycles);
              case 5:
                _context3.t0 = _context3.sent;
                _yield$loadApp = (0, _context3.t0)();
                mount = _yield$loadApp.mount;
                otherMicroAppConfigs = _objectWithoutProperties(_yield$loadApp, _excluded2);
                return _context3.abrupt("return", _objectSpread({
                  mount: [/*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {
                    return _regeneratorRuntime.wrap(function _callee$(_context) {
                      while (1) switch (_context.prev = _context.next) {
                        case 0:
                          return _context.abrupt("return", loader(true));
                        case 1:
                        case "end":
                          return _context.stop();
                      }
                    }, _callee);
                  }))].concat(_toConsumableArray(toArray(mount)), [/*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {
                    return _regeneratorRuntime.wrap(function _callee2$(_context2) {
                      while (1) switch (_context2.prev = _context2.next) {
                        case 0:
                          return _context2.abrupt("return", loader(false));
                        case 1:
                        case "end":
                          return _context2.stop();
                      }
                    }, _callee2);
                  }))])
                }, otherMicroAppConfigs));
              case 10:
              case "end":
                return _context3.stop();
            }
          }, _callee3);
        }));
        function app() {
          return _app.apply(this, arguments);
        }
        return app;
      }(),
      activeWhen: activeRule,
      customProps: props
    });
  });
}



// 2. 【single-spa】先去single-spa库
// 对微应用的相关属性做预准备，主要用到promise控制流程

const apps = [];

// 2.1 入口的注册函数
export function registerApplication(
  appNameOrConfig,
  appOrLoadApp,
  activeWhen,
  customProps
) {

  // 整合并检验所有的入参
  const registration = sanitizeArguments(
    appNameOrConfig,
    appOrLoadApp,
    activeWhen,
    customProps
  );

  if (!isStarted() && !startWarningInitialized) {
    startWarningInitialized = true;

    setTimeout(() => {
      if (!isStarted()) {
        console.warn(
          formatErrorMessage(
            1,
            __DEV__ &&
              `singleSpa.start() has not been called, 5000ms after single-spa was loaded. Before start() is called, apps can be declared and loaded, but not bootstrapped or mounted.`
          )
        );
      }
    }, 5000);
  }

  if (getAppNames().indexOf(registration.name) !== -1)
    throw Error(
      formatErrorMessage(
        21,
        __DEV__ &&
          `There is already an app registered with name ${registration.name}`,
        registration.name
      )
    );

  // 在api.js文件处的apps数组注入一个对象，每个对象是一个微应用
  apps.push(
    assign(
      {
        loadErrorTime: null,
        // !！！注意初始化的状态是【NOT_LOADED】
        status: NOT_LOADED,
        parcels: {},
        devtools: {
          overlays: {
            options: {},
            selectors: [],
          },
        },
      },
      registration
    )
  );

  // 进入reroute核心函数，监听url的情况
  if (isInBrowser) {
    ensureJQuerySupport();
    reroute();
  }
}

function sanitizeArguments(
  appNameOrConfig,
  appOrLoadApp,
  activeWhen,
  customProps
) {
  const usingObjectAPI = typeof appNameOrConfig === "object";

  const registration = {
    name: null,
    loadApp: null,
    activeWhen: null,
    customProps: null,
  };

  // 把主应用写的配置全部挂到这个对象上面
  if (usingObjectAPI) {
    validateRegisterWithConfig(appNameOrConfig);
    registration.name = appNameOrConfig.name;
    registration.loadApp = appNameOrConfig.app;
    registration.activeWhen = appNameOrConfig.activeWhen;
    registration.customProps = appNameOrConfig.customProps;
  } else {
    validateRegisterWithArguments(
      appNameOrConfig,
      appOrLoadApp,
      activeWhen,
      customProps
    );
    registration.name = appNameOrConfig;
    registration.loadApp = appOrLoadApp;
    registration.activeWhen = activeWhen;
    registration.customProps = customProps;
  }

  registration.loadApp = sanitizeLoadApp(registration.loadApp);
  registration.customProps = sanitizeCustomProps(registration.customProps);
  registration.activeWhen = sanitizeActiveWhen(registration.activeWhen);

  return registration;
}


// 2.2 reroute函数：分发当前的生命周期阶段函数
// 要么已经start去performAppChanges，要么还没start去loadApps

export function reroute(
  pendingPromises = [],
  eventArguments,
  silentNavigation = false
) {
  if (appChangeUnderway) {
    return new Promise((resolve, reject) => {
      peopleWaitingOnAppChange.push({
        resolve,
        reject,
        eventArguments,
      });
    });
  }

  let startTime, profilerKind;

  if (__PROFILE__) {
    startTime = performance.now();
    if (silentNavigation) {
      profilerKind = "silentNavigation";
    } else if (eventArguments) {
      profilerKind = "browserNavigation";
    } else {
      profilerKind = "triggerAppChange";
    }
  }

  // 2.2.1 拿到当前对应生命周期的微应用对象
  const { appsToUnload, appsToUnmount, appsToLoad, appsToMount } =
    getAppChanges();
  
  let appsThatChanged,
    cancelPromises = [],
    oldUrl = currentUrl,
    newUrl = (currentUrl = window.location.href);

  // 2.2.2 检查当前是否已经执行了start函数
  if (isStarted()) {
    appChangeUnderway = true;
    appsThatChanged = appsToUnload.concat(
      appsToLoad,
      appsToUnmount,
      appsToMount
    );
    return performAppChanges();

  } else {
    // 2.2.3 还没执行就需要执行loadApps函数，去加载微应用
    appsThatChanged = appsToLoad;
    return loadApps();
  }

  function cancelNavigation(val = true) {
    const promise =
      typeof val?.then === "function" ? val : Promise.resolve(val);
    cancelPromises.push(
      promise.catch((err) => {
        console.warn(
          Error(
            formatErrorMessage(
              42,
              __DEV__ &&
                `single-spa: A cancelNavigation promise rejected with the following value: ${err}`
            )
          )
        );
        console.warn(err);

        // Interpret a Promise rejection to mean that the navigation should not be canceled
        return false;
      })
    );
  }

  // 2.2.3 加载微应用
  function loadApps() {
    // 返回一个promise，这个时候调用栈已经没有函数了，去到then执行回调
    return Promise.resolve().then(() => {

      // 加载每一个微应用，【确保每一微应用的生命周期函数得到保存】
      const loadPromises = appsToLoad.map(toLoadPromise);
      let succeeded;

      return (
        Promise.all(loadPromises)
          .then(callAllEventListeners)
          // there are no mounted apps, before start() is called, so we always return []
          .then(() => {
            if (__PROFILE__) {
              succeeded = true;
            }

            return [];
          })
          .catch((err) => {
            if (__PROFILE__) {
              succeeded = false;
            }

            callAllEventListeners();
            throw err;
          })
          .finally(() => {
            if (__PROFILE__) {
              addProfileEntry(
                "routing",
                "loadApps",
                profilerKind,
                startTime,
                performance.now(),
                succeeded
              );
            }
          })
      );
    });
  }

  function performAppChanges() {
    return Promise.resolve().then(() => {
      // https://github.com/single-spa/single-spa/issues/545
      fireSingleSpaEvent(
        appsThatChanged.length === 0
          ? "before-no-app-change"
          : "before-app-change",
        getCustomEventDetail(true)
      );

      fireSingleSpaEvent(
        "before-routing-event",
        getCustomEventDetail(true, { cancelNavigation })
      );

      return Promise.all(cancelPromises).then((cancelValues) => {
        const navigationIsCanceled = cancelValues.some((v) => v);

        if (navigationIsCanceled) {
          // Change url back to old url, without triggering the normal single-spa reroute
          originalReplaceState.call(
            window.history,
            history.state,
            "",
            oldUrl.substring(location.origin.length)
          );

          // Single-spa's internal tracking of current url needs to be updated after the url change above
          currentUrl = location.href;

          // necessary for the reroute function to know that the current reroute is finished
          appChangeUnderway = false;

          if (__PROFILE__) {
            addProfileEntry(
              "routing",
              "navigationCanceled",
              profilerKind,
              startTime,
              performance.now(),
              true
            );
          }

          // Tell single-spa to reroute again, this time with the url set to the old URL
          return reroute(pendingPromises, eventArguments, true);
        }

        const unloadPromises = appsToUnload.map(toUnloadPromise);

        const unmountUnloadPromises = appsToUnmount
          .map(toUnmountPromise)
          .map((unmountPromise) => unmountPromise.then(toUnloadPromise));

        const allUnmountPromises = unmountUnloadPromises.concat(unloadPromises);

        const unmountAllPromise = Promise.all(allUnmountPromises);

        let unmountFinishedTime;

        unmountAllPromise.then(
          () => {
            if (__PROFILE__) {
              unmountFinishedTime = performance.now();

              addProfileEntry(
                "routing",
                "unmountAndUnload",
                profilerKind,
                startTime,
                performance.now(),
                true
              );
            }
            fireSingleSpaEvent(
              "before-mount-routing-event",
              getCustomEventDetail(true)
            );
          },
          (err) => {
            if (__PROFILE__) {
              addProfileEntry(
                "routing",
                "unmountAndUnload",
                profilerKind,
                startTime,
                performance.now(),
                true
              );
            }

            throw err;
          }
        );

        /* We load and bootstrap apps while other apps are unmounting, but we
         * wait to mount the app until all apps are finishing unmounting
         */
        const loadThenMountPromises = appsToLoad.map((app) => {
          return toLoadPromise(app).then((app) =>
            tryToBootstrapAndMount(app, unmountAllPromise)
          );
        });

        /* These are the apps that are already bootstrapped and just need
         * to be mounted. They each wait for all unmounting apps to finish up
         * before they mount.
         */
        const mountPromises = appsToMount
          .filter((appToMount) => appsToLoad.indexOf(appToMount) < 0)
          .map((appToMount) => {
            return tryToBootstrapAndMount(appToMount, unmountAllPromise);
          });
        return unmountAllPromise
          .catch((err) => {
            callAllEventListeners();
            throw err;
          })
          .then(() => {
            /* Now that the apps that needed to be unmounted are unmounted, their DOM navigation
             * events (like hashchange or popstate) should have been cleaned up. So it's safe
             * to let the remaining captured event listeners to handle about the DOM event.
             */
            callAllEventListeners();

            return Promise.all(loadThenMountPromises.concat(mountPromises))
              .catch((err) => {
                pendingPromises.forEach((promise) => promise.reject(err));
                throw err;
              })
              .then(finishUpAndReturn)
              .then(
                () => {
                  if (__PROFILE__) {
                    addProfileEntry(
                      "routing",
                      "loadAndMount",
                      profilerKind,
                      unmountFinishedTime,
                      performance.now(),
                      true
                    );
                  }
                },
                (err) => {
                  if (__PROFILE__) {
                    addProfileEntry(
                      "routing",
                      "loadAndMount",
                      profilerKind,
                      unmountFinishedTime,
                      performance.now(),
                      false
                    );
                  }

                  throw err;
                }
              );
          });
      });
    });
  }

  function finishUpAndReturn() {
    const returnValue = getMountedApps();
    pendingPromises.forEach((promise) => promise.resolve(returnValue));

    try {
      const appChangeEventName =
        appsThatChanged.length === 0 ? "no-app-change" : "app-change";
      fireSingleSpaEvent(appChangeEventName, getCustomEventDetail());
      fireSingleSpaEvent("routing-event", getCustomEventDetail());
    } catch (err) {
      /* We use a setTimeout because if someone else's event handler throws an error, single-spa
       * needs to carry on. If a listener to the event throws an error, it's their own fault, not
       * single-spa's.
       */
      setTimeout(() => {
        throw err;
      });
    }

    /* Setting this allows for subsequent calls to reroute() to actually perform
     * a reroute instead of just getting queued behind the current reroute call.
     * We want to do this after the mounting/unmounting is done but before we
     * resolve the promise for the `reroute` function.
     */
    appChangeUnderway = false;

    if (peopleWaitingOnAppChange.length > 0) {
      /* While we were rerouting, someone else triggered another reroute that got queued.
       * So we need reroute again.
       */
      const nextPendingPromises = peopleWaitingOnAppChange;
      peopleWaitingOnAppChange = [];
      reroute(nextPendingPromises);
    }

    return returnValue;
  }

  /* We need to call all event listeners that have been delayed because they were
   * waiting on single-spa. This includes haschange and popstate events for both
   * the current run of performAppChanges(), but also all of the queued event listeners.
   * We want to call the listeners in the same order as if they had not been delayed by
   * single-spa, which means queued ones first and then the most recent one.
   */
  function callAllEventListeners() {
    // During silent navigation (when navigation was canceled and we're going back to the old URL),
    // we should not fire any popstate / hashchange events
    if (!silentNavigation) {
      pendingPromises.forEach((pendingPromise) => {
        callCapturedEventListeners(pendingPromise.eventArguments);
      });

      callCapturedEventListeners(eventArguments);
    }
  }

  function getCustomEventDetail(isBeforeChanges = false, extraProperties) {
    const newAppStatuses = {};
    const appsByNewStatus = {
      // for apps that were mounted
      [MOUNTED]: [],
      // for apps that were unmounted
      [NOT_MOUNTED]: [],
      // apps that were forcibly unloaded
      [NOT_LOADED]: [],
      // apps that attempted to do something but are broken now
      [SKIP_BECAUSE_BROKEN]: [],
    };

    if (isBeforeChanges) {
      appsToLoad.concat(appsToMount).forEach((app, index) => {
        addApp(app, MOUNTED);
      });
      appsToUnload.forEach((app) => {
        addApp(app, NOT_LOADED);
      });
      appsToUnmount.forEach((app) => {
        addApp(app, NOT_MOUNTED);
      });
    } else {
      appsThatChanged.forEach((app) => {
        addApp(app);
      });
    }

    const result = {
      detail: {
        newAppStatuses,
        appsByNewStatus,
        totalAppChanges: appsThatChanged.length,
        originalEvent: eventArguments?.[0],
        oldUrl,
        newUrl,
      },
    };

    if (extraProperties) {
      assign(result.detail, extraProperties);
    }

    return result;

    function addApp(app, status) {
      const appName = toName(app);
      status = status || getAppStatus(appName);
      newAppStatuses[appName] = status;
      const statusArr = (appsByNewStatus[status] =
        appsByNewStatus[status] || []);
      statusArr.push(appName);
    }
  }

  function fireSingleSpaEvent(name, eventProperties) {
    // During silent navigation (caused by navigation cancelation), we should not
    // fire any single-spa events
    if (!silentNavigation) {
      window.dispatchEvent(
        new CustomEvent(`single-spa:${name}`, eventProperties)
      );
    }
  }
}


export function getAppChanges() {
  const appsToUnload = [],
    appsToUnmount = [],
    appsToLoad = [],
    appsToMount = [];

  // We re-attempt to download applications in LOAD_ERROR after a timeout of 200 milliseconds
  const currentTime = new Date().getTime();

  // 在入口的注册函数，apps已经被加入了一个微应用，并且status为NOT_LOADED
  apps.forEach((app) => {
    const appShouldBeActive =
      app.status !== SKIP_BECAUSE_BROKEN && shouldBeActive(app);

    switch (app.status) {
      case LOAD_ERROR:
        if (appShouldBeActive && currentTime - app.loadErrorTime >= 200) {
          appsToLoad.push(app);
        }
        break;

      // 如果是没有加载或者正在加载的，归类到appsToLoad数组
      case NOT_LOADED:
      case LOADING_SOURCE_CODE:
        if (appShouldBeActive) {
          appsToLoad.push(app);
        }
        break;
      case NOT_BOOTSTRAPPED:
      case NOT_MOUNTED:
        if (!appShouldBeActive && getAppUnloadInfo(toName(app))) {
          appsToUnload.push(app);
        } else if (appShouldBeActive) {
          appsToMount.push(app);
        }
        break;
      case MOUNTED:
        if (!appShouldBeActive) {
          appsToUnmount.push(app);
        }
        break;
      // all other statuses are ignored
    }
  });

  return { appsToUnload, appsToUnmount, appsToLoad, appsToMount };
}


// 检查当前是否已经执行了start函数
let started = false;

export function start(opts) {
  started = true;
  if (isInBrowser) {
    patchHistoryApi(opts);
    reroute();
  }
}

export function isStarted() {
  return started;
}






// 2.3 每个微应用的加载函数：改状态为正在加载 + 等待外部qiankun的app函数执行完毕


export function toLoadPromise(appOrParcel) {
  // 入参是apps数组里面的微应用整合对象

  // 返回一个promise，因为父函数后面也是promise，因此回到这里继续执行then回调
  return Promise.resolve().then(() => {
    // 判断这个微应用对象是否已经处理过了
    if (appOrParcel.loadPromise) {
      return appOrParcel.loadPromise;
    }

    if (
      appOrParcel.status !== NOT_LOADED &&
      appOrParcel.status !== LOAD_ERROR
    ) {
      return appOrParcel;
    }

    let startTime;

    if (__PROFILE__) {
      startTime = performance.now();
    }

    // 在这里状态改为【正在加载】
    appOrParcel.status = LOADING_SOURCE_CODE;

    let appOpts, isUserErr;

    // 继续返回一个promise，外部的all方法需要等到每个promise内部的所有then都执行完毕，外层的promise才会变为resolve状态
    // 因此这里继续执行这个then回调
    return (appOrParcel.loadPromise = Promise.resolve()
      .then(() => {

        // 拿到之前在sanitizeArguments函数挂到微应用对象上面的loadApp函数，也就是qiankun库里面registerMicroApps函数传入的app函数
        // 执行这个函数，得到一个promise（_asyncToGenerator函数生成的）
        const loadPromise = appOrParcel.loadApp(getProps(appOrParcel));
        if (!smellsLikeAPromise(loadPromise)) {
          isUserErr = true;
          throw Error(
            formatErrorMessage(
              33,
              __DEV__ &&
                `single-spa loading function did not return a promise. Check the second argument to registerApplication('${toName(
                  appOrParcel
                )}', loadingFunction, activityFunction)`,
              toName(appOrParcel)
            )
          );
        }

        // 然后挂上then函数，需要等到loadPromise完成，也就是子应用加载完毕！！
        // 至此回到【qiankun】的代码主战场
        return loadPromise.then((val) => {
          appOrParcel.loadErrorTime = null;

          appOpts = val;

          let validationErrMessage, validationErrCode;

          if (typeof appOpts !== "object") {
            validationErrCode = 34;
            if (__DEV__) {
              validationErrMessage = `does not export anything`;
            }
          }

          if (
            // ES Modules don't have the Object prototype
            Object.prototype.hasOwnProperty.call(appOpts, "bootstrap") &&
            !validLifecycleFn(appOpts.bootstrap)
          ) {
            validationErrCode = 35;
            if (__DEV__) {
              validationErrMessage = `does not export a valid bootstrap function or array of functions`;
            }
          }

          if (!validLifecycleFn(appOpts.mount)) {
            validationErrCode = 36;
            if (__DEV__) {
              validationErrMessage = `does not export a mount function or array of functions`;
            }
          }

          if (!validLifecycleFn(appOpts.unmount)) {
            validationErrCode = 37;
            if (__DEV__) {
              validationErrMessage = `does not export a unmount function or array of functions`;
            }
          }

          const type = objectType(appOpts);

          if (validationErrCode) {
            let appOptsStr;
            try {
              appOptsStr = JSON.stringify(appOpts);
            } catch {}
            console.error(
              formatErrorMessage(
                validationErrCode,
                __DEV__ &&
                  `The loading function for single-spa ${type} '${toName(
                    appOrParcel
                  )}' resolved with the following, which does not have bootstrap, mount, and unmount functions`,
                type,
                toName(appOrParcel),
                appOptsStr
              ),
              appOpts
            );
            handleAppError(
              validationErrMessage,
              appOrParcel,
              SKIP_BECAUSE_BROKEN
            );
            return appOrParcel;
          }

          if (appOpts.devtools && appOpts.devtools.overlays) {
            appOrParcel.devtools.overlays = assign(
              {},
              appOrParcel.devtools.overlays,
              appOpts.devtools.overlays
            );
          }

          appOrParcel.status = NOT_BOOTSTRAPPED;
          appOrParcel.bootstrap = flattenFnArray(appOpts, "bootstrap");
          appOrParcel.mount = flattenFnArray(appOpts, "mount");
          appOrParcel.unmount = flattenFnArray(appOpts, "unmount");
          appOrParcel.unload = flattenFnArray(appOpts, "unload");
          appOrParcel.timeouts = ensureValidAppTimeouts(appOpts.timeouts);

          delete appOrParcel.loadPromise;

          if (__PROFILE__) {
            addProfileEntry(
              "application",
              toName(appOrParcel),
              "load",
              startTime,
              performance.now(),
              true
            );
          }

          return appOrParcel;
        });
      })
      .catch((err) => {
        delete appOrParcel.loadPromise;

        let newStatus;
        if (isUserErr) {
          newStatus = SKIP_BECAUSE_BROKEN;
        } else {
          newStatus = LOAD_ERROR;
          appOrParcel.loadErrorTime = new Date().getTime();
        }
        handleAppError(err, appOrParcel, newStatus);

        if (__PROFILE__) {
          addProfileEntry(
            "application",
            toName(appOrParcel),
            "load",
            startTime,
            performance.now(),
            false
          );
        }

        return appOrParcel;
      }));
  });
}






// 3. 【回到qiankun】加载微应用


export function loadApp(_x3) {
  return _loadApp.apply(this, arguments);
}
function _loadApp() {
  _loadApp = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee17(app) {
    var _sandboxContainer, _sandboxContainer$ins;
    var configuration,
      lifeCycles,
      entry,
      appName,
      appInstanceId,
      markName,
      _configuration$singul,
      singular,
      _configuration$sandbo,
      sandbox,
      excludeAssetFilter,
      _configuration$global,
      globalContext,
      importEntryOpts,
      _yield$importEntry,
      template,
      execScripts,
      assetPublicPath,
      getExternalScripts,
      appContent,
      strictStyleIsolation,
      scopedCSS,
      initialAppWrapperElement,
      initialContainer,
      legacyRender,
      render,
      initialAppWrapperGetter,
      global,
      mountSandbox,
      unmountSandbox,
      useLooseSandbox,
      speedySandbox,
      sandboxContainer,
      _mergeWith,
      _mergeWith$beforeUnmo,
      beforeUnmount,
      _mergeWith$afterUnmou,
      afterUnmount,
      _mergeWith$afterMount,
      afterMount,
      _mergeWith$beforeMoun,
      beforeMount,
      _mergeWith$beforeLoad,
      beforeLoad,
      scriptExports,
      _getLifecyclesFromExp,
      bootstrap,
      mount,
      unmount,
      update,
      _getMicroAppStateActi,
      onGlobalStateChange,
      setGlobalState,
      offGlobalStateChange,
      syncAppWrapperElement2Sandbox,
      parcelConfigGetter,
      _args17 = arguments;
    return _regeneratorRuntime.wrap(function _callee17$(_context17) {
      while (1) switch (_context17.prev = _context17.next) {

        // 3.1 第一步：通过fetch方法拉取写在主应用的配置项那边的entry的url的微应用资源
        case 0:
          configuration = _args17.length > 1 && _args17[1] !== undefined ? _args17[1] : {};
          lifeCycles = _args17.length > 2 ? _args17[2] : undefined;
          entry = app.entry, appName = app.name;
          appInstanceId = genAppInstanceIdByName(appName);
          markName = "[qiankun] App ".concat(appInstanceId, " Loading");
          if (process.env.NODE_ENV === 'development') {
            performanceMark(markName);
          }
          _configuration$singul = configuration.singular, singular = _configuration$singul === void 0 ? false : _configuration$singul, _configuration$sandbo = configuration.sandbox, sandbox = _configuration$sandbo === void 0 ? true : _configuration$sandbo, excludeAssetFilter = configuration.excludeAssetFilter, _configuration$global = configuration.globalContext, globalContext = _configuration$global === void 0 ? window : _configuration$global, importEntryOpts = _objectWithoutProperties(configuration, _excluded); // get the entry html content and script executor
          _context17.next = 9;

          // !!!内部通过fetch请求这个entry的url
          return importEntry(entry, importEntryOpts);
        case 9:
          _yield$importEntry = _context17.sent;
          template = _yield$importEntry.template;
          execScripts = _yield$importEntry.execScripts;
          assetPublicPath = _yield$importEntry.assetPublicPath;
          getExternalScripts = _yield$importEntry.getExternalScripts;
          _context17.next = 16;
          return getExternalScripts();
        case 16:
          _context17.next = 18;
          return validateSingularMode(singular, app);
        case 18:
          if (!_context17.sent) {
            _context17.next = 21;
            break;
          }
          _context17.next = 21;
          return prevAppUnmountedDeferred && prevAppUnmountedDeferred.promise;
        case 21:
          appContent = getDefaultTplWrapper(appInstanceId, sandbox)(template);
          strictStyleIsolation = _typeof(sandbox) === 'object' && !!sandbox.strictStyleIsolation;
          if (process.env.NODE_ENV === 'development' && strictStyleIsolation) {
            console.warn("[qiankun] strictStyleIsolation configuration will be removed in 3.0, pls don't depend on it or use experimentalStyleIsolation instead!");
          }
          scopedCSS = isEnableScopedCSS(sandbox);
          initialAppWrapperElement = createElement(appContent, strictStyleIsolation, scopedCSS, appInstanceId);
          initialContainer = 'container' in app ? app.container : undefined;
          legacyRender = 'render' in app ? app.render : undefined;
          render = getRender(appInstanceId, appContent, legacyRender); // 第一次加载设置应用可见区域 dom 结构
          // 确保每次应用加载前容器 dom 结构已经设置完毕
          render({
            element: initialAppWrapperElement,
            loading: true,
            container: initialContainer
          }, 'loading');
          initialAppWrapperGetter = getAppWrapperGetter(appInstanceId, !!legacyRender, strictStyleIsolation, scopedCSS, function () {
            return initialAppWrapperElement;
          });
          global = globalContext;
          mountSandbox = function mountSandbox() {
            return Promise.resolve();
          };
          unmountSandbox = function unmountSandbox() {
            return Promise.resolve();
          };
          useLooseSandbox = _typeof(sandbox) === 'object' && !!sandbox.loose; // enable speedy mode by default
          speedySandbox = _typeof(sandbox) === 'object' ? sandbox.speedy !== false : true;
          if (sandbox) {
            sandboxContainer = createSandboxContainer(appInstanceId,
            // FIXME should use a strict sandbox logic while remount, see https://github.com/umijs/qiankun/issues/518
            initialAppWrapperGetter, scopedCSS, useLooseSandbox, excludeAssetFilter, global, speedySandbox);
            // 用沙箱的代理对象作为接下来使用的全局对象
            global = sandboxContainer.instance.proxy;
            mountSandbox = sandboxContainer.mount;
            unmountSandbox = sandboxContainer.unmount;
          }
          _mergeWith = _mergeWith2({}, getAddOns(global, assetPublicPath), lifeCycles, function (v1, v2) {
            return _concat(v1 !== null && v1 !== void 0 ? v1 : [], v2 !== null && v2 !== void 0 ? v2 : []);
          }), _mergeWith$beforeUnmo = _mergeWith.beforeUnmount, beforeUnmount = _mergeWith$beforeUnmo === void 0 ? [] : _mergeWith$beforeUnmo, _mergeWith$afterUnmou = _mergeWith.afterUnmount, afterUnmount = _mergeWith$afterUnmou === void 0 ? [] : _mergeWith$afterUnmou, _mergeWith$afterMount = _mergeWith.afterMount, afterMount = _mergeWith$afterMount === void 0 ? [] : _mergeWith$afterMount, _mergeWith$beforeMoun = _mergeWith.beforeMount, beforeMount = _mergeWith$beforeMoun === void 0 ? [] : _mergeWith$beforeMoun, _mergeWith$beforeLoad = _mergeWith.beforeLoad, beforeLoad = _mergeWith$beforeLoad === void 0 ? [] : _mergeWith$beforeLoad;
          _context17.next = 40;
          return execHooksChain(toArray(beforeLoad), app, global);
        case 40:
          _context17.next = 42;
          return execScripts(global, sandbox && !useLooseSandbox, {
            scopedGlobalVariables: speedySandbox ? cachedGlobals : []
          });
        case 42:
          scriptExports = _context17.sent;
          _getLifecyclesFromExp = getLifecyclesFromExports(scriptExports, appName, global, (_sandboxContainer = sandboxContainer) === null || _sandboxContainer === void 0 ? void 0 : (_sandboxContainer$ins = _sandboxContainer.instance) === null || _sandboxContainer$ins === void 0 ? void 0 : _sandboxContainer$ins.latestSetProp), bootstrap = _getLifecyclesFromExp.bootstrap, mount = _getLifecyclesFromExp.mount, unmount = _getLifecyclesFromExp.unmount, update = _getLifecyclesFromExp.update;
          _getMicroAppStateActi = getMicroAppStateActions(appInstanceId), onGlobalStateChange = _getMicroAppStateActi.onGlobalStateChange, setGlobalState = _getMicroAppStateActi.setGlobalState, offGlobalStateChange = _getMicroAppStateActi.offGlobalStateChange; // FIXME temporary way
          syncAppWrapperElement2Sandbox = function syncAppWrapperElement2Sandbox(element) {
            return initialAppWrapperElement = element;
          };
          parcelConfigGetter = function parcelConfigGetter() {
            var remountContainer = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialContainer;
            var appWrapperElement;
            var appWrapperGetter;
            var parcelConfig = {
              name: appInstanceId,
              bootstrap: bootstrap,
              mount: [/*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {
                var marks;
                return _regeneratorRuntime.wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      if (process.env.NODE_ENV === 'development') {
                        marks = performanceGetEntriesByName(markName, 'mark'); // mark length is zero means the app is remounting
                        if (marks && !marks.length) {
                          performanceMark(markName);
                        }
                      }
                    case 1:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              })), /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {
                return _regeneratorRuntime.wrap(function _callee3$(_context3) {
                  while (1) switch (_context3.prev = _context3.next) {
                    case 0:
                      _context3.next = 2;
                      return validateSingularMode(singular, app);
                    case 2:
                      _context3.t0 = _context3.sent;
                      if (!_context3.t0) {
                        _context3.next = 5;
                        break;
                      }
                      _context3.t0 = prevAppUnmountedDeferred;
                    case 5:
                      if (!_context3.t0) {
                        _context3.next = 7;
                        break;
                      }
                      return _context3.abrupt("return", prevAppUnmountedDeferred.promise);
                    case 7:
                      return _context3.abrupt("return", undefined);
                    case 8:
                    case "end":
                      return _context3.stop();
                  }
                }, _callee3);
              })),
              /*#__PURE__*/
              // initial wrapper element before app mount/remount
              _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {
                return _regeneratorRuntime.wrap(function _callee4$(_context4) {
                  while (1) switch (_context4.prev = _context4.next) {
                    case 0:
                      appWrapperElement = initialAppWrapperElement;
                      appWrapperGetter = getAppWrapperGetter(appInstanceId, !!legacyRender, strictStyleIsolation, scopedCSS, function () {
                        return appWrapperElement;
                      });
                    case 2:
                    case "end":
                      return _context4.stop();
                  }
                }, _callee4);
              })),
              /*#__PURE__*/
              // 添加 mount hook, 确保每次应用加载前容器 dom 结构已经设置完毕
              _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {
                var useNewContainer;
                return _regeneratorRuntime.wrap(function _callee5$(_context5) {
                  while (1) switch (_context5.prev = _context5.next) {
                    case 0:
                      useNewContainer = remountContainer !== initialContainer;
                      if (useNewContainer || !appWrapperElement) {
                        // element will be destroyed after unmounted, we need to recreate it if it not exist
                        // or we try to remount into a new container
                        appWrapperElement = createElement(appContent, strictStyleIsolation, scopedCSS, appInstanceId);
                        syncAppWrapperElement2Sandbox(appWrapperElement);
                      }
                      render({
                        element: appWrapperElement,
                        loading: true,
                        container: remountContainer
                      }, 'mounting');
                    case 3:
                    case "end":
                      return _context5.stop();
                  }
                }, _callee5);
              })), mountSandbox,
              /*#__PURE__*/
              // exec the chain after rendering to keep the behavior with beforeLoad
              _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {
                return _regeneratorRuntime.wrap(function _callee6$(_context6) {
                  while (1) switch (_context6.prev = _context6.next) {
                    case 0:
                      return _context6.abrupt("return", execHooksChain(toArray(beforeMount), app, global));
                    case 1:
                    case "end":
                      return _context6.stop();
                  }
                }, _callee6);
              })), /*#__PURE__*/function () {
                var _ref7 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7(props) {
                  return _regeneratorRuntime.wrap(function _callee7$(_context7) {
                    while (1) switch (_context7.prev = _context7.next) {
                      case 0:
                        return _context7.abrupt("return", mount(_objectSpread(_objectSpread({}, props), {}, {
                          container: appWrapperGetter(),
                          setGlobalState: setGlobalState,
                          onGlobalStateChange: onGlobalStateChange
                        })));
                      case 1:
                      case "end":
                        return _context7.stop();
                    }
                  }, _callee7);
                }));
                return function (_x4) {
                  return _ref7.apply(this, arguments);
                };
              }(),
              /*#__PURE__*/
              // finish loading after app mounted
              _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8() {
                return _regeneratorRuntime.wrap(function _callee8$(_context8) {
                  while (1) switch (_context8.prev = _context8.next) {
                    case 0:
                      return _context8.abrupt("return", render({
                        element: appWrapperElement,
                        loading: false,
                        container: remountContainer
                      }, 'mounted'));
                    case 1:
                    case "end":
                      return _context8.stop();
                  }
                }, _callee8);
              })), /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9() {
                return _regeneratorRuntime.wrap(function _callee9$(_context9) {
                  while (1) switch (_context9.prev = _context9.next) {
                    case 0:
                      return _context9.abrupt("return", execHooksChain(toArray(afterMount), app, global));
                    case 1:
                    case "end":
                      return _context9.stop();
                  }
                }, _callee9);
              })),
              /*#__PURE__*/
              // initialize the unmount defer after app mounted and resolve the defer after it unmounted
              _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10() {
                return _regeneratorRuntime.wrap(function _callee10$(_context10) {
                  while (1) switch (_context10.prev = _context10.next) {
                    case 0:
                      _context10.next = 2;
                      return validateSingularMode(singular, app);
                    case 2:
                      if (!_context10.sent) {
                        _context10.next = 4;
                        break;
                      }
                      prevAppUnmountedDeferred = new Deferred();
                    case 4:
                    case "end":
                      return _context10.stop();
                  }
                }, _callee10);
              })), /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee11() {
                var measureName;
                return _regeneratorRuntime.wrap(function _callee11$(_context11) {
                  while (1) switch (_context11.prev = _context11.next) {
                    case 0:
                      if (process.env.NODE_ENV === 'development') {
                        measureName = "[qiankun] App ".concat(appInstanceId, " Loading Consuming");
                        performanceMeasure(measureName, markName);
                      }
                    case 1:
                    case "end":
                      return _context11.stop();
                  }
                }, _callee11);
              }))],
              unmount: [/*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee12() {
                return _regeneratorRuntime.wrap(function _callee12$(_context12) {
                  while (1) switch (_context12.prev = _context12.next) {
                    case 0:
                      return _context12.abrupt("return", execHooksChain(toArray(beforeUnmount), app, global));
                    case 1:
                    case "end":
                      return _context12.stop();
                  }
                }, _callee12);
              })), /*#__PURE__*/function () {
                var _ref13 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee13(props) {
                  return _regeneratorRuntime.wrap(function _callee13$(_context13) {
                    while (1) switch (_context13.prev = _context13.next) {
                      case 0:
                        return _context13.abrupt("return", unmount(_objectSpread(_objectSpread({}, props), {}, {
                          container: appWrapperGetter()
                        })));
                      case 1:
                      case "end":
                        return _context13.stop();
                    }
                  }, _callee13);
                }));
                return function (_x5) {
                  return _ref13.apply(this, arguments);
                };
              }(), unmountSandbox, /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee14() {
                return _regeneratorRuntime.wrap(function _callee14$(_context14) {
                  while (1) switch (_context14.prev = _context14.next) {
                    case 0:
                      return _context14.abrupt("return", execHooksChain(toArray(afterUnmount), app, global));
                    case 1:
                    case "end":
                      return _context14.stop();
                  }
                }, _callee14);
              })), /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee15() {
                return _regeneratorRuntime.wrap(function _callee15$(_context15) {
                  while (1) switch (_context15.prev = _context15.next) {
                    case 0:
                      render({
                        element: null,
                        loading: false,
                        container: remountContainer
                      }, 'unmounted');
                      offGlobalStateChange(appInstanceId);
                      // for gc
                      appWrapperElement = null;
                      syncAppWrapperElement2Sandbox(appWrapperElement);
                    case 4:
                    case "end":
                      return _context15.stop();
                  }
                }, _callee15);
              })), /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee16() {
                return _regeneratorRuntime.wrap(function _callee16$(_context16) {
                  while (1) switch (_context16.prev = _context16.next) {
                    case 0:
                      _context16.next = 2;
                      return validateSingularMode(singular, app);
                    case 2:
                      _context16.t0 = _context16.sent;
                      if (!_context16.t0) {
                        _context16.next = 5;
                        break;
                      }
                      _context16.t0 = prevAppUnmountedDeferred;
                    case 5:
                      if (!_context16.t0) {
                        _context16.next = 7;
                        break;
                      }
                      prevAppUnmountedDeferred.resolve();
                    case 7:
                    case "end":
                      return _context16.stop();
                  }
                }, _callee16);
              }))]
            };
            if (typeof update === 'function') {
              parcelConfig.update = update;
            }
            return parcelConfig;
          };
          return _context17.abrupt("return", parcelConfigGetter);
        case 48:
        case "end":
          return _context17.stop();
      }
    }, _callee17);
  }));
  return _loadApp.apply(this, arguments);
}



 
// 3.1 【import-html-entry】通过fetch拉取微应用的html资源

export function importEntry(entry) {
  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _opts$fetch3 = opts.fetch,
    fetch = _opts$fetch3 === void 0 ? defaultFetch : _opts$fetch3,
    _opts$getTemplate = opts.getTemplate,
    getTemplate = _opts$getTemplate === void 0 ? defaultGetTemplate : _opts$getTemplate,
    postProcessTemplate = opts.postProcessTemplate;
  var getPublicPath = opts.getPublicPath || opts.getDomain || defaultGetPublicPath;
  if (!entry) {
    throw new SyntaxError('entry should not be empty!');
  }


  if (typeof entry === 'string') {

    // 去拉取这个url的index.html
    return importHTML(entry, {
      fetch: fetch,
      getPublicPath: getPublicPath,
      getTemplate: getTemplate,
      postProcessTemplate: postProcessTemplate
    });
  }

  // config entry
  if (Array.isArray(entry.scripts) || Array.isArray(entry.styles)) {
    var _entry$scripts = entry.scripts,
      scripts = _entry$scripts === void 0 ? [] : _entry$scripts,
      _entry$styles = entry.styles,
      styles = _entry$styles === void 0 ? [] : _entry$styles,
      _entry$html = entry.html,
      html = _entry$html === void 0 ? '' : _entry$html;
    var getHTMLWithStylePlaceholder = function getHTMLWithStylePlaceholder(tpl) {
      return styles.reduceRight(function (html, styleSrc) {
        return "".concat(genLinkReplaceSymbol(styleSrc)).concat(html);
      }, tpl);
    };
    var getHTMLWithScriptPlaceholder = function getHTMLWithScriptPlaceholder(tpl) {
      return scripts.reduce(function (html, scriptSrc) {
        return "".concat(html).concat(genScriptReplaceSymbol(scriptSrc));
      }, tpl);
    };
    return getEmbedHTML(getTemplate(getHTMLWithScriptPlaceholder(getHTMLWithStylePlaceholder(html))), styles, {
      fetch: fetch
    }).then(function (embedHTML) {
      return {
        template: embedHTML,
        assetPublicPath: getPublicPath(entry),
        getExternalScripts: function getExternalScripts() {
          return _getExternalScripts(scripts, fetch);
        },
        getExternalStyleSheets: function getExternalStyleSheets() {
          return _getExternalStyleSheets(styles, fetch);
        },
        execScripts: function execScripts(proxy, strictGlobal) {
          var opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
          if (!scripts.length) {
            return Promise.resolve();
          }
          return _execScripts(scripts[scripts.length - 1], scripts, proxy, _objectSpread({
            fetch: fetch,
            strictGlobal: strictGlobal
          }, opts));
        }
      };
    });
  } else {
    throw new SyntaxError('entry scripts or styles should be array!');
  }
}


export default function importHTML(url) {
  // url就是entry，比如https://xxx.com
  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var fetch = defaultFetch;
  var autoDecodeResponse = false;
  var getPublicPath = defaultGetPublicPath;
  var getTemplate = defaultGetTemplate;
  var postProcessTemplate = opts.postProcessTemplate;

  // fetch方法支持外部的配置
  if (typeof opts === 'function') {
    fetch = opts;
  } else {
    if (opts.fetch) {
      if (typeof opts.fetch === 'function') {
        fetch = opts.fetch;
      } else {
        fetch = opts.fetch.fn || defaultFetch;
        autoDecodeResponse = !!opts.fetch.autoDecodeResponse;
      }
    }
    getPublicPath = opts.getPublicPath || opts.getDomain || defaultGetPublicPath;
    getTemplate = opts.getTemplate || defaultGetTemplate;
  }

  // 用fetch拉取url资源
  return embedHTMLCache[url] || (embedHTMLCache[url] = fetch(url).then(function (response) {
    // 这里拿到的response是一个对象，长这样：
    // body : (...)
    // bodyUsed: false
    // headers: Headers {}
    // ok: true
    // redirected: false
    // status: 200
    // statusText: "OK"
    // type: "cors"
    // url: "https://jdxota-beta-ui.jdl.cn/"

    // 读取response的body，第二个参数autoDecodeResponse是false
    return readResAsString(response, autoDecodeResponse);
  }).then(function (html) {
    // 这个时候的html是子应用的整个index.html的字符串

    // 去defaultGetPublicPath，返回子应用末尾加上了/的url
    var assetPublicPath = getPublicPath(url);

    // 处理html（用正则提取style和script资源，保存到数组，返回一个汇总对象）
    var _processTpl = processTpl(getTemplate(html), assetPublicPath, postProcessTemplate),
      template = _processTpl.template,
      scripts = _processTpl.scripts,
      entry = _processTpl.entry,
      styles = _processTpl.styles;

    return getEmbedHTML(template, styles, {
      fetch: fetch
    }).then(function (embedHTML) {
      return {
        template: embedHTML,
        assetPublicPath: assetPublicPath,
        getExternalScripts: function getExternalScripts() {
          return _getExternalScripts(scripts, fetch);
        },
        getExternalStyleSheets: function getExternalStyleSheets() {
          return _getExternalStyleSheets(styles, fetch);
        },
        execScripts: function execScripts(proxy, strictGlobal) {
          var opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
          if (!scripts.length) {
            return Promise.resolve();
          }
          return _execScripts(entry, scripts, proxy, _objectSpread({
            fetch: fetch,
            strictGlobal: strictGlobal
          }, opts));
        }
      };
    });
  }));
}

export function readResAsString(response, autoDetectCharset) {
  // 未启用自动检测
  if (!autoDetectCharset) {
    // 返回的 Response 对象会提供 .text() 方法
    // 用于将响应体（Response Body）解析为字符串
    return response.text();
  }

  // 如果没headers，发生在test环境下的mock数据，为兼容原有测试用例
  if (!response.headers) {
    return response.text();
  }

  // 如果没返回content-type，走默认逻辑
  var contentType = response.headers.get('Content-Type');
  if (!contentType) {
    return response.text();
  }

  // 解析content-type内的charset
  // Content-Type: text/html; charset=utf-8
  // Content-Type: multipart/form-data; boundary=something
  // GET请求下不会出现第二种content-type
  var charset = 'utf-8';
  var parts = contentType.split(';');
  if (parts.length === 2) {
    var _parts$1$split = parts[1].split('='),
      _parts$1$split2 = _slicedToArray(_parts$1$split, 2),
      value = _parts$1$split2[1];
    var encoding = value && value.trim();
    if (encoding) {
      charset = encoding;
    }
  }

  // 如果还是utf-8，那么走默认，兼容原有逻辑，这段代码删除也应该工作
  if (charset.toUpperCase() === 'UTF-8') {
    return response.text();
  }

  // 走流读取，编码可能是gbk，gb2312等，比如sofa 3默认是gbk编码
  return response.blob().then(function (file) {
    return new Promise(function (resolve, reject) {
      var reader = new window.FileReader();
      reader.onload = function () {
        resolve(reader.result);
      };
      reader.onerror = reject;
      reader.readAsText(file, charset);
    });
  });
}


export function defaultGetPublicPath(entry) {
  if (_typeof(entry) === 'object') {
    return '/';
  }
  try {
    // entry是子应用的url，location.href是主应用的当前的url
    var _URL = new URL(entry, location.href),
      // origin是子应用的url，等于entry
      origin = _URL.origin,
      // pathname是/
      pathname = _URL.pathname;
    var paths = pathname.split('/');
    paths.pop();
    // 最后得到的url是https://xxx.com/
    // 子应用的entry末尾加上了/
    return "".concat(origin).concat(paths.join('/'), "/");
  } catch (e) {
    console.warn(e);
    return '';
  }
}

export default function processTpl(tpl, baseURI, postProcessTemplate) {
  // 入参：
  // tpl是整个html字符串
  // baseURI是子应用末尾加上了/的url
  // postProcessTemplate是undefined

  var scripts = [];
  var styles = [];
  var entry = null;
  var moduleSupport = isModuleScriptSupported();
  var template = tpl

  // 1. 去掉注释
  .replace(HTML_COMMENT_REGEX, '')
  .replace(HTML_COMMENT_REGEX, '')

  // 2. 处理link标签
  .replace(LINK_TAG_REGEX, function (match) {
    var styleType = !!match.match(STYLE_TYPE_REGEX);
    if (styleType) {
      var styleHref = match.match(STYLE_HREF_REGEX);
      var styleIgnore = match.match(LINK_IGNORE_REGEX);
      // 如果是style类型的link标签，并且有href属性
      if (styleHref) {
        var href = styleHref && styleHref[2];
        var newHref = href;
        // 构造一个完整的url，类似于"https://xxx.com/css/main-111.min.css"
        if (href && !hasProtocol(href)) {
          newHref = getEntirePath(href, baseURI);
        }
        if (styleIgnore) {
          return genIgnoreAssetReplaceSymbol(newHref);
        }
        newHref = parseUrl(newHref);
        // url保存到style数组
        styles.push(newHref);
        return genLinkReplaceSymbol(newHref);
      }
    }
    var preloadOrPrefetchType = match.match(LINK_PRELOAD_OR_PREFETCH_REGEX) && match.match(LINK_HREF_REGEX) && !match.match(LINK_AS_FONT);
    if (preloadOrPrefetchType) {
      var _match$match = match.match(LINK_HREF_REGEX),
        _match$match2 = _slicedToArray(_match$match, 3),
        linkHref = _match$match2[2];
      return genLinkReplaceSymbol(linkHref, true);
    }
    return match;
  })

  // 3. 处理style标签
  .replace(STYLE_TAG_REGEX, function (match) {
    if (STYLE_IGNORE_REGEX.test(match)) {
      return genIgnoreAssetReplaceSymbol('style file');
    }
    return match;
  })

  // 4. 处理script标签（每个script标签都会处理）
  .replace(ALL_SCRIPT_REGEX, function (match, scriptTag) {
    var scriptIgnore = scriptTag.match(SCRIPT_IGNORE_REGEX);
    var moduleScriptIgnore = moduleSupport && !!scriptTag.match(SCRIPT_NO_MODULE_REGEX) || !moduleSupport && !!scriptTag.match(SCRIPT_MODULE_REGEX);

    // 获取script标签的type属性
    var matchedScriptTypeMatch = scriptTag.match(SCRIPT_TYPE_REGEX);
    var matchedScriptType = matchedScriptTypeMatch && matchedScriptTypeMatch[2];
    if (!isValidJavaScriptType(matchedScriptType)) {
      return match;
    }

    // 如果是外部的script标签
    if (SCRIPT_TAG_REGEX.test(match) && scriptTag.match(SCRIPT_SRC_REGEX)) {

      // 获取script标签的src属性
      var matchedScriptEntry = scriptTag.match(SCRIPT_ENTRY_REGEX);
      var matchedScriptSrcMatch = scriptTag.match(SCRIPT_SRC_REGEX);
      var matchedScriptSrc = matchedScriptSrcMatch && matchedScriptSrcMatch[2];
      if (entry && matchedScriptEntry) {
        throw new SyntaxError('You should not set multiply entry script!');
      }

      // 构造一个完整的url，类似于"https://xxx.com/js/main-111.js"
      if (matchedScriptSrc) {
        if (!hasProtocol(matchedScriptSrc)) {
          // 原本的src是相对路径，没有前面的协议，在这里构造一个完整的url
          matchedScriptSrc = getEntirePath(matchedScriptSrc, baseURI);
        }
        matchedScriptSrc = parseUrl(matchedScriptSrc);
      }
      entry = entry || matchedScriptEntry && matchedScriptSrc;
      if (scriptIgnore) {
        return genIgnoreAssetReplaceSymbol(matchedScriptSrc || 'js file');
      }
      if (moduleScriptIgnore) {
        return genModuleScriptReplaceSymbol(matchedScriptSrc || 'js file', moduleSupport);
      }

      // 保存到scripts数组
      if (matchedScriptSrc) {
        var asyncScript = !!scriptTag.match(SCRIPT_ASYNC_REGEX);
        var crossOriginScript = !!scriptTag.match(SCRIPT_CROSSORIGIN_REGEX);
        scripts.push(asyncScript || crossOriginScript ? {
          async: asyncScript,
          src: matchedScriptSrc,
          crossOrigin: crossOriginScript
        } : matchedScriptSrc);

        // 加上一些来自于插件的注释
        return genScriptReplaceSymbol(matchedScriptSrc, asyncScript, crossOriginScript);
      }
      return match;
    } else {
      if (scriptIgnore) {
        return genIgnoreAssetReplaceSymbol('js file');
      }
      if (moduleScriptIgnore) {
        return genModuleScriptReplaceSymbol('js file', moduleSupport);
      }

      // if it is an inline script
      var code = getInlineCode(match);

      // remove script blocks when all of these lines are comments.
      var isPureCommentBlock = code.split(/[\r\n]+/).every(function (line) {
        return !line.trim() || line.trim().startsWith('//');
      });
      if (!isPureCommentBlock) {
        scripts.push(match);
      }
      return inlineScriptReplaceSymbol;
    }
  });

  // 全部处理结束之后
  scripts = scripts.filter(function (script) {
    return !!script;
  });
  var tplResult = {
    template: template,
    scripts: scripts,
    styles: styles,
    entry: entry || scripts[scripts.length - 1]
  };
  if (typeof postProcessTemplate === 'function') {
    tplResult = postProcessTemplate(tplResult);
  }

  // 汇总全部资源路径，返回一个对象
  return tplResult;
}

function getEntirePath(path, baseURI) {
  return new URL(path, baseURI).toString();
}

// 转换 url 中的转义字符，例如 &amp; => &
export function parseUrl(url) {
  var parser = new DOMParser();
  var html = "<script src=\"".concat(url, "\"></script>");
  var doc = parser.parseFromString(html, "text/html");
  return doc.scripts[0].src;
}

export var genLinkReplaceSymbol = function genLinkReplaceSymbol(linkHref) {
  var preloadOrPrefetch = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  return "<!-- ".concat(preloadOrPrefetch ? 'prefetch/preload' : '', " link ").concat(linkHref, " replaced by import-html-entry -->");
};

export var genScriptReplaceSymbol = function genScriptReplaceSymbol(scriptSrc) {
  var async = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  var crossOrigin = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  return "<!-- ".concat(crossOrigin ? 'cors' : '', " ").concat(async ? 'async' : '', " script ").concat(scriptSrc, " replaced by import-html-entry -->");
};

function getEmbedHTML(template, styles) {
  var opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  var _opts$fetch = opts.fetch,
    fetch = _opts$fetch === void 0 ? defaultFetch : _opts$fetch;
  var embedHTML = template;

  // 处理外部的style资源
  return _getExternalStyleSheets(styles, fetch).then(function (styleSheets) {
    embedHTML = styleSheets.reduce(function (html, styleSheet) {
      var styleSrc = styleSheet.src;
      var styleSheetContent = styleSheet.value;
      html = html.replace(genLinkReplaceSymbol(styleSrc), isInlineCode(styleSrc) ? "".concat(styleSrc) : "<style>/* ".concat(styleSrc, " */").concat(styleSheetContent, "</style>"));
      return html;
    }, embedHTML);
    return embedHTML;
  });
}


function _getExternalStyleSheets(styles) {
  var fetch = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultFetch;
  return allSettledButCanBreak(styles.map( /*#__PURE__*/function () {
    var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(styleLink) {
      return _regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!isInlineCode(styleLink)) {
              _context.next = 4;
              break;
            }
            return _context.abrupt("return", getInlineCode(styleLink));
          case 4:
            return _context.abrupt("return", styleCache[styleLink] || (styleCache[styleLink] = fetch(styleLink).then(function (response) {
              if (response.status >= 400) {
                throw new Error("".concat(styleLink, " load failed with status ").concat(response.status));
              }
              return response.text();
            })["catch"](function (e) {
              try {
                if (e.message.indexOf(styleLink) === -1) {
                  e.message = "".concat(styleLink, " ").concat(e.message);
                }
              } catch (_) {
                // e.message 可能是 readonly，此时会触发异常
              }
              throw e;
            })));
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }())).then(function (results) {
    return results.map(function (result, i) {
      if (result.status === 'fulfilled') {
        result.value = {
          src: styles[i],
          value: result.value
        };
      }
      return result;
    }).filter(function (result) {
      // 忽略失败的请求，避免异常下载阻塞后续资源加载
      if (result.status === 'rejected') {
        Promise.reject(result.reason);
      }
      return result.status === 'fulfilled';
    }).map(function (result) {
      return result.value;
    });
  });
}

var isInlineCode = function isInlineCode(code) {
  return code.startsWith('<');
};