<!DOCTYPE html>
<head>
    <style>
        body{
            margin: 0;
        }
        a{
            color: #00bc9b;
            color: #d3d3d3
        }
        span {
            width: 100px;
            height: 100px;
            padding-left: 20px;
            margin-left: 20px;
        }
        .anim1 {
            position: relative;
            margin: 50px;
            width: 100px;
            height: 50px;
            text-align: center;
            background-color: #d3d3d3;
            line-height: 50px;
            transition: all 1s ease-in-out;
        }
        .color {
            position: relative;
            transition: all 1s ease-in-out;
            z-index: 1;
        }
        .color::before {
            position: absolute;
            content: "";
            top: 0;
            left: 0;
            display: block;
            width: 100%;
            height: 100%;
            z-index: -1;
            transform-origin: 0 50%;
            transform: scaleX(0);
            transition: all 1s ease-in-out;
        }
        .color:hover::before {
            background-color: #00bc9b;
            transform: scaleX(1);
        }
        /* .anim1:hover {
            background-color: #00bc9b;
        } */
        .underline::after {
            position: absolute;
            content: '';
            left: 0;
            bottom: 0;
            width: 0;
            height: 5px;
            background-color: brown;
            transition: all 1s ease-in-out;
        }
        .underline:hover::after {
            width: 100%;
        }


        /* .heart {
            border-radius: 5px;
            animation: identifier 1s ease-in-out infinite;
        }
        @keyframes identifier {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        } */
        /* .shape {
            transform: skew(15deg);
        } */



    </style>
    <!-- <link href="./style.css" rel="stylesheet"/> -->
</head>
<body>

    <div style="position: relative;">
        <div class="22" style="padding: 50px; margin: 50px; height: 50px; width: 50px; background-color: grey; border: 5px solid black;"></div>
        <div class="33" style="padding: 50px; margin: 50px; height: 50px; width: 50px; background-color: grey; border: 5px solid black; box-sizing: border-box;"></div>
    </div>

    <input class="input"/>
    <button class="button">go other page</button>
    <div class="anim1 underline heart shape color">hello</div>

    <!-- <script src="./JSFoundation/EventLoop/promise.js"></script> -->

    <div>
        <strong></strong>
        <span></span>
        <!-- this is a comment -->
        123
        <p class="11"></p>
        <i></i>
        345354
        <!-- 4575675 -->
        <address></address>
    </div>


    <div class="outter" style="position: absolute; left: 0px; top: 0px; width: 100px; height: 100px; background-color: #d3d3d3;">
        <div class="middle" style="width: 50px; height: 50px; background-color: #666666;">
            <div class="inner" style="width: 25px; height: 25px; background-color: #282828;">
            </div>
        </div>
    </div>


    <input value="请输入" onfocus="if (this.value === '请输入') { this.value = '' }" onblur="if (this.value === '') { this.value = '请输入' }" />
    <input placeholder="请输入" />





    <script>
        // const div1 = document.getElementsByClassName('22')[0];
        // const div2 = document.getElementsByClassName('33')[0];
        // console.log(
        //     div1.getBoundingClientRect(),
        //     div2.getBoundingClientRect(),
        //     div1.offsetTop,
        //     div2.offsetTop,
        //     div1.offsetLeft,
        //     div2.offsetLeft,
        //     div1.offsetParent,
        // )



        // const div1 = document.getElementsByClassName('22')[0];
        // const div2 = document.getElementsByClassName('33')[0];
        // let timer = null;
        // let key = true;
        // div1.onclick(function () {
        //     // 默认锁是打开的，允许执行第一次
        //     // 函数执行完，关闭锁，下次定时函数还没被清除的时候，不能再次执行
        //     if (key) {
        //         timer = setInterval(() => {
        //             window.scrollBy(0, 10)
        //         }, 10);
        //         key = false;
        //     }
        // });
        // div2.onclick = function () {
        //     clearInterval(timer);
        //     key = true;
        // }



        // let key = true;
        // div1.onclick = function () {
        //     if(key) {
        //         div1.style.backgroundColor = 'green';
        //         key = false;
        //     } else {
        //         div1.style.backgroundColor = 'grey';
        //         key = true;
        //     }
        // }


        // // 打印两次aa
        // div1.addEventListener('click', function () {
        //     console.log('aa')
        // }, false)
        // div1.addEventListener('click', function () {
        //     console.log('aa')
        // }, false)


        // // 只会打印一次aa
        // div1.addEventListener('click', test, false)
        // div1.addEventListener('click', test, false)
        // function test() {
        //     console.log('aa')
        // }


        // div1.attachEvent('onclick', function () {
        //     test.call(div1)
        // })


        const outter = document.getElementsByClassName('outter')[0];
        const middle = document.getElementsByClassName('middle')[0];
        const inner = document.getElementsByClassName('inner')[0];


        // outter.addEventListener('click', () => {
        //     console.log('outter')
        // }, true)
        // middle.addEventListener('click', () => {
        //     console.log('middle')
        // }, true)


        // outter.addEventListener('click', () => {
        //     console.log('outter Bubble')
        // }, false)
        // middle.addEventListener('click', () => {
        //     console.log('middle Bubble')
        // }, false)
        inner.addEventListener('click', () => {
            console.log('inner Bubble')
        }, true)
        inner.addEventListener('click', () => {
            console.log('inner')
        }, true)


        // // 写法一
        // a.onclick = function () {
        //     return false;
        // }

        // // 写法二
        // // javascript:后面如果写void()，括号内表示函数的返回值
        // <a href="javascript:void(false)">click me</a>



        // 按下鼠标，但不松开
        outter.onmousedown = function (e) {

            // 按下鼠标的这一刻，鼠标可能在方块的中间
            // 但移动的那一刻，鼠标位置恢复成默认（dom的坐标原点（左上角））

            // 这对于dom方块来说呢？？？它往右下移动了
            // （为什么要思考对于方块来说如何，因为移动时改变的是方块的位置而不是鼠标的位置）

            // 移动的时刻：给方块往左上拉，减去【按下时鼠标距离方块原点】的x和y距离
            let disX = e.pageX - parseInt(outter.style.left);
            let disY = e.pageY - parseInt(outter.style.top);

            // 鼠标一直按着，开始移动
            // 注意！按着鼠标移动的时候，对移动事件的监听由document来触发，这样允许鼠标移出了方块也能触发到
            document.onmousemove = function (e) {
                const event = e || window.event;
                outter.style.left = event.pageX - disX + 'px';
                outter.style.top = event.pageY - disY + 'px';
            }

            // 松开鼠标
            // 松开鼠标允许在方块之外松开
            document.onmouseup = function () {
                document.onmousemove = null
            }
        }



        // let firstTime = 0, lastTime = 0;
        // let onClickKey = false;

        // document.onmousedown = function () {
        //     firstTime = new Date().getTime();
        // }
        // document.onmouseup = function () {
        //     lastTime = new Date().getTime();
        //     if (lastTime - firstTime < 300) {
        //         onClickKey = true;
        //     }
        // }
        // document.onclick = function () {
        //     if (onClickKey) {
        //         console.log('click');
        //         onClickKey = false;
        //     }
        // }


        // document.onkeydown = function (e) {
        //     console.log('down', e)
        // }

        // document.onkeypress = function (e) {
        //     console.log('press', e)
        //     console.log(String.fromCharCode(e.charCode))
        // }



        // function asyncLoadScript(url, callback) {
        //     const script = document.createElement('script');
        //     script.type = 'text/javascript';

        //     if (script.readyState) {
        //         script.onreadystatechange = function () {
        //             if (script.readyState === 'complete' || script.readyState === 'loaded') {
        //                 callback();
        //             }
        //         }
        //     } else {
        //         script.onload = function () {
        //             callback();
        //         }
        //     }

        //     // 为什么要把回调函数写在下载前？？？？？
        //     // 如果下载写在回调函数前面，万一下载是一瞬间的事，还没执行到【定义回调函数】的代码时，就已经下载完，错过了onload的时机
        //     // 回调函数即使被定义也永远不会执行了
        //     script.src = url;
        //     document.head.appendChild(script);
        // }


        // asyncLoadScript('tools.js', test)
        // // 直接写tools.js里面的test的函数名报错
        // // 执行到上面这一行的时候，预编译并不知道asyncLoadScript的具体执行内容，所以在这test找不到

        // // 使用函数引用的方法，传入一个新函数，里面包裹目标函数
        // // 在这不需要知道新函数的函数体，传入到回调函数，回调函数真正执行的时候，tools.js已经下载完毕，test肯定就能找到了
        // asyncLoadScript('tools.js', function () { test() })



        // // 办法二，传入字符串，用eval()执行
        // script.onload = function () {
        //     eval(callback);
        // }
        // asyncLoadScript('tools.js', 'test()')



        // // 办法三：tools.js导出一个对象，里面的属性对应的就是一个方法，直接调用属性
        // // const tools = {
        // //     test: function () {},
        // //     demo: function () {},
        // // }
        // script.onload = function () {
        //     // tools是导出的对象名字
        //     tools[callback]();
        // }
        // asyncLoadScript('tools.js', 'test')



    </script>

    <script>
        // const ele = document.getElementsByClassName('11')[0]
        // // console.log(div.firstChild)

        // function getSiblingElement(ele, n) {
        //     while(ele && n) {
        //         console.log('enter while ele', ele, '---n', n)
        //         if (n > 0) {
        //             // 考虑到nextElementSibling的ie9兼容性问题，可以使用两个方案
        //             // 第一个方案使用ele = ele.nextElementSibling;
        //             // 另一个方案使用兼容性更强的nextSibling

        //             // 思路是：首先移动到下一个，然后发现下一个不是元素节点，继续移动指针；如果下一个是元素节点，停下循环
        //             // 特殊情况是ele为null，if里面执行不了，但也退出内部的while循环了，接着也退出外部的while循环了
        //             while(ele = ele.nextSibling) {
        //                 if (ele.nodeType === 1) break;
        //             }
        //             n --;
        //         } else {
        //             while(ele = ele.previousSibling) {
        //                 if (ele.nodeType === 1) break;
        //             }
        //             n ++;
        //         }
        //     }
        //     return ele;
        // }
        // console.log(getSiblingElement(ele, -100))



        // console.log(

        //     // ele.nextElementSibling.nextElementSibling
        //     // ele.nextElementSibling

        // )




    </script>

    <script>

        // const input = document.querySelector('.input')
        // input.addEventListener('input', () => test())
        // function test() {
        //     console.log('on!!')
        // }

        // window.addEventListener('beforeunload', () => console.log('window beforeunload'))

        // const btn = document.querySelector('.button')
        // btn.addEventListener('click', () => {
        //     window.location.href = 'https://www.baidu.com'
        // })

        // console.log(navigator.userAgent)

        // if ("virtualKeyboard" in navigator) {
        //     navigator.virtualKeyboard.overlaysContent = true;

        //     navigator.virtualKeyboard.addEventListener("geometrychange", (event) => {
        //         const { x, y, width, height } = event.target.boundingRect;
        //     });
        // }

    </script>



  <script>



    // const obj = {
    //     a:1,
    //     b:{
    //         c:2,
    //         d:4,
    //     },
    //     e:{
    //         f: {
    //             g:5,
    //             h:6,
    //         },
    //         i:[1, 2, 3],
    //     },
    //     j: 8,
    //     k: {
    //         l: 9,
    //     },
    // }

    // function isObj(obj) {
    //     if (typeof obj === 'object' && obj !== null) {
    //         return true
    //     }
    //     return false
    // }
    // function copy(obj) {
    //     if (!isObj(obj)) return
    //     const keys = Object.keys(obj)
    //     let layer = Array.isArray(obj) ? [] : {}
    //     for (let i = 0; i < keys.length; i++) {
    //         const curKey = keys[i]
    //         if (isObj(obj[curKey])) {
    //             const nextLayer = copy(obj[curKey])
    //             layer[curKey] = nextLayer
    //         } else {
    //             layer[curKey] = obj[curKey]
    //         }
    //     }
    //     return layer
    // }
    // const res = copy(obj)

    // // res.e.i[2] = 10

    // console.log('res', res)
    // console.log('obj', obj)



    // function isObj(obj) {
    //   if (typeof obj === 'object' && obj !== null) {
    //     return true
    //   }
    //   return false
    // }
    // function deepDiff(obj1, obj2) {
    //   if (!isObj(obj1) || !isObj(obj2)) {
    //     let isSame = false
    //     if (!isObj(obj1) && !isObj(obj2)) {
    //       if (obj1 === obj2) {
    //         isSame = true
    //       }
    //     }
    //     return isSame
    //   }
    //   let keys1 = Object.keys(obj1)
    //   let keys2 = Object.keys(obj2)
    //   if (keys1.length !== keys2.length) {
    //     return false
    //   }
    //   for (let i of keys1) {
    //     const res = deepDiff(obj1[i], obj2[i])
    //     if (!res) {
    //       return false
    //     }
    //   }
    //   return true
    // }
    // const r = deepDiff(obj, res)
    // console.log('compare', r)


    // // 深度对比的useDeepEffect,重写useEffect
    // function useDeepEffect(callback, arr) {
    //   if (!Array.isArray(arr)) return new Error('not arr')
    //   const pre = useRef(arr)
    //   const init = useRef(false)
    //   if (!init.current) {
    //     callback.apply(this, arguments)
    //     init.current = true
    //   } else {
    //     const isSame = deepDiff(arr, pre.current)
    //     if (!isSame) {
    //       callback.apply(this, arguments)
    //       pre.current = arr
    //     }
    //   }
    // }



    // 返回store
    // 中间件作为applyMiddleWare的参数传入createStoreconst
    // store = createStore(reducer, initState={},applyMiddleWare(reduxThunk))

    // const listeners = []
    // let state = 7

    // function dispatch(x, from) {
    //     console.log('dispatch from', from)
    //     console.log('dispatch change', x + 7)
    //     state = state + x

    //     if (listeners.length) {
    //         listeners.map((item) => item())
    //     }

    //     return x + 1
    // }

    // function subscribe(callback) {
    //     listeners.push(callback)
    // }

    // // const add4 = (dp) => (a4) => {
    // //     // console.log('dp4', dp)
    // //     // console.log('a4', a4)
    // //     console.log('4 before state', state)
    // //     const newAction = dp(a4, '4')
    // //     console.log('4 after state', state)
    // //     // console.log('changed a4', newAction)
    // //     return newAction
    // // }
    // const add3 = (dp) => (a3) => {
    //     // console.log('dp3', dp)
    //     // console.log('a3', a3)
    //     console.log('3 before state', state)
    //     const newAction = dp(a3, '3')
    //     // console.log('changed a3', newAction)
    //     console.log('3 after state', state)
    //     return newAction
    // }
    // const add2 = (dp) => (a2) => {
    //     // console.log('dp2', dp)
    //     // console.log('a2', a2)
    //     console.log('2 before state', state)
    //     const newAction = dp(a2, '2')
    //     console.log('2 after state', state)
    //     // console.log('changed a2', newAction)
    //     return newAction
    // }
    // const add1 = (dp) => (a1) => {
    //     // console.log('dp1', dp)
    //     // console.log('a1', a1)
    //     console.log('1 before state', state)
    //     const newAction = dp(a1, '1')
    //     console.log('1 after state', state)
    //     // console.log('changed a1', newAction)
    //     return newAction
    // }
    // let mw = [add1, add2, add3]

    // function compose(...args) {
    //     return args.reduce((accum, item) => (...args) => accum(item(...args)))
    // }

    // subscribe(() => console.log('11111'))

    // const res = compose(...mw)(dispatch)
    // // console.log('res', res)
    // const e = res(1)
    // console.log('e', e)




    // // const fn = (...args)=>add1(add2(add3(add4(...args))))
    // // const res = fn(dispatch)
    // // console.log('res',res)
    // // res(2)



    // let originDispatch = (...args) => {console.log(...args)}
    // const middleware1 = (dispatch) => {  
    //     return (...args) => {
    //         console.log('middleware1 before dispatch')
    //         dispatch(...args)
    //         console.log('middleware1 after dispatch')
    //     }
    // }
    // const middleware2 = (dispatch) => {
    //     return (...args) => {
    //         console.log('middleware2 before dispatch')
    //         dispatch(...args)
    //         console.log('middleware2 before dispatch')
    //     }
    // }
    // originDispatch = middleware2(middleware1(originDispatch))
    // originDispatch('ruby', 'cool', 'language')




    // // const fn1 = next => a1 => next(a1)
    // // const fn2 = next => a2 => next(a2)
    // // const fn3 = next => a3 => next(a3)


    // function fn1(next) {
    //     return function (a1) {
    //         console.log('1 before dispatch')
    //         const n = next(a1, '1')
    //         console.log('1 after dispatch')
    //         return n
    //     }
    // }
    // function fn2(next) {
    //     return function (a2) {
    //         console.log('2 before dispatch')
    //         const n = next(a2, '2')
    //         console.log('2 after dispatch')
    //         return n
    //     }
    // }
    // function fn3(next) {
    //     return function (a3) {
    //         console.log('3 before dispatch')
    //         const n = next(a3, '3')
    //         console.log('3 after dispatch')
    //         return n
    //     }
    // }

    // const dp = (x, from) => {
    //     console.log('dp from', x + 1, from)
    // }

    // const r = fn1(fn2(fn3(dispatch)))
    // r('hello')



    // const data = [
    //     '3',
    //     '5',
    //     '16:00 30 90',
    //     '17:00 15 120',
    //     '16:20 28 80',
    //     '10:05 30 120',
    //     '09:02 50 70',
    //     '5',
    //     '16:00 30 90',
    //     '17:29 15 120',
    //     '16:20 28 80',
    //     '10:05 20 120',
    //     '09:02 10 70',
    //     '5',
    //     '16:00 30 90',
    //     '17:30 15 120',
    //     '16:20 28 80',
    //     '10:05 20 120',
    //     '09:02 10 70',
    // ]

    // const timeline = []
    // const rawData = data.slice(1)
    // for (let i = 0; i < rawData.length; i++) {
    //     const curLine = rawData[i]
    //     if (curLine.split(' ').length <= 1) {
    //         let singleServer = []
    //         for (let j = i + 1; j < +curLine + i + 1; j++) {
    //             singleServer.push(rawData[j])
    //         }
    //         timeline.push(singleServer)
    //     } else {
    //         continue
    //     }
    // }
    // console.log(timeline)

    // function transferHHMMToMM(str) {
    //     const hour = +str.split(':')[0]
    //     const minute = +str.split(':')[1]
    //     return hour*60+minute
    // }

    // for (let i = 0; i < timeline.length; i++) {
    //     const curLine = timeline[i]
    //     let map = {}
    //     curLine.forEach((item, index) => {
    //         const arr = item.split(' ')
    //         const startTime = transferHHMMToMM(arr[0])
    //         const onlineNum = +arr[1]
    //         const duration = +arr[2]
    //         const endTime = startTime + duration
    //         if (map[startTime]) {
    //             map[startTime] += onlineNum
    //         } else {
    //             map[startTime] = onlineNum
    //         }
    //         if (map[endTime]) {
    //             map[endTime] -= onlineNum
    //         } else {
    //             map[endTime] = -onlineNum
    //         }
    //     })
    //     let maxOnlinePlayers = 0, curOnlinePlayer = 0
    //     for (let i in map) {
    //         curOnlinePlayer += map[i]
    //         maxOnlinePlayers = Math.max(maxOnlinePlayers, curOnlinePlayer)
    //     }
    //     console.log(maxOnlinePlayers)
    // }


    // function sleep(time) {
    //     return new Promise((resolve, reject) => {
    //         setTimeout(() => {
    //         resolve('time')
    //         console.log('inner item', new Date().getMilliseconds())
    //         }, time * 4000)
    //     })
    // }
    // const arr = [1, 2, 3, 4, 5]
    // const promiseAll = arr.map((item) => {
    //     console.log('outer item', item, new Date().getMilliseconds())
    //     return sleep(1)
    // })

    // Promise.all(promiseAll).then(res => console.log('all done'))
    
    // sleep(2).then(res => console.log('sleep 2'))


    // const str = '(({{[}}))'
    // function isValid(str) {
    //     const stack = []
    //     const map = new Map()
    //     map.set(')', '(')
    //     map.set('}', '{')
    //     map.set(']', '[')

    //     for (let i = 0; i < str.length; i++) {
    //         if (['(', '{', '['].includes(str[i])) {
    //             stack.push(str[i])
    //         } else {
    //             if (stack.length === 0 || map.get(str[i]) !== stack.pop()) return false
    //         }
    //     }
    //     if (stack.length === 0) {
    //         return true
    //     }
    // }
    // const res = isValid(str)
    // console.log(res)


    // function isValid2(str) {
    //     let left = 0, right = str.length - 1
    //     while (left <= right && left < str.length && right >= 0) {
    //         if (str[left] !== str[right]) {
    //             return false
    //         }
    //     }
    // }





    // // 手写promise.all
    // function promiseAll(promises) {
    //     return new Promise((resolve, reject) => {
    //         if (!Array.isArray(promises)) return new Error('not array')
    //         let result = []
    //         let resCount = 0
    //         for (let i = 0; i < promises.length; i++) {
    //             Promise.resolve(promises[i]).then(res => {
    //                 resCount++
    //                 result[i] = res
    //                 if (resCount === promises.length) {
    //                     return resolve(result)
    //                 }
    //             }).catch(err => {
    //                 return reject(err)
    //             })
    //         }
    //     })
    // }

    // const s = 'A man, a plan, a canal: Panama'
    // const resg = /\W/g
    // const str = s.toLowerCase().replace(resg, '')
    // console.log(str)


    // const nodes = [
    //     { id: 3, name: '节点C', parentId: 1 },
    //     { id: 6, name: '节点F', parentId: 3 },
    //     { id: 0, name: 'root', parentId: null },
    //     { id: 1, name: '节点A', parentId: 0 },
    //     { id: 8, name: '节点H', parentId: 4 },
    //     { id: 4, name: '节点D', parentId: 1 },
    //     { id: 2, name: '节点B', parentId: 0 },
    //     { id: 5, name: '节点E', parentId: 2 },
    //     { id: 7, name: '节点G', parentId: 2 },
    //     { id: 9, name: '节点I', parentId: 5 }
    // ];

    // function arrToTree(arr) {
    //     const map = {}
    //     let res = null
    //     arr.forEach(item => {
    //         map[item.id] = item;
    //     });
    //     console.log('map', map)
    //     for (let i = 0; i < arr.length; i++) {
    //         const item = arr[i]
    //         const parent = map[item.parentId]
    //         if (parent) {
    //             if (parent.children) {
    //                 parent.children.push(item)
    //             } else {
    //                 parent.children = [item]
    //             }
    //             console.log(map[item.parentId])
    //         } else {
    //             res = item
    //         }
    //     }
    //     return res
    // }


    // const a = arrToTree(nodes)
    // console.log('rrr',a)


    // const data = [
    //     '5',
    //     '11001'
    // ]
    // const arr = data[1].split('')
    // console.log(arr)

    // function reverse(arr, l, r) {
    //     let start = l
    //     let end = r
    //     while(start < end && start < arr.length && end >= 0) {
    //         let temp = arr[start]
    //         arr[start] = arr[end]
    //         arr[end] = temp
    //         start++
    //         end--
    //     }
    // }

    // // 不同长度窗口
    // let minNum = Infinity
    // for (let i = 1; i < arr.length; i++) {
    //     let left = 0, right = 0
    //     while(right < arr.length && left < arr.length) {
    //         if (right - left === i - 1) {
    //             reverse(arr, left, right)
    //             const curNum = Number(arr.join(''))
    //             if (curNum < minNum) {
    //                 minNum = arr.join('')
    //             }
    //             left++
    //         }
    //         right++
    //     }
    // }
    // console.log('min', minNum)



    // const data = [
    //     '3',
    //     '5 10',
    //     '3 2 3 4 5',
    //     '3 3',
    //     '1 1 1',
    //     '10 10',
    //     '3 1 2 8 5 4 2 9 12 7',
    // ]
    // const groupData = []
    // for (let i = 1; i < data.length; i = i + 2) {
    //     groupData.push([data[i].split(' ').map(Number), data[i+1].split(' ').map(Number)])
    // }

    // for (let i = 0; i < groupData.length; i++) {
    //     // 每个组数据
    //     const needCrew = Math.ceil(groupData[i][0][0] / 2)
    //     const needEnergy = groupData[i][0][1]
    //     const allCrewEnergy = groupData[i][1].sort((a, b) => a - b)

    //     let path = [], sumEnergy = 0
    //     function backtracking(startIndex) {
    //         if (path.length === needCrew) {
    //             const sum = path.reduce((accum, item) => {
    //                 return accum + item
    //             })
    //             if (sum >= needEnergy) {
    //                 sumEnergy++
    //             }
    //             return
    //         }
    //         for (let j = startIndex; j < allCrewEnergy.length; j++) {
    //             path.push(allCrewEnergy[j])
    //             backtracking(j+1)
    //             path.pop()
    //         }
    //     }
    //     backtracking(0)
    //     console.log(sumEnergy)
    // }





    // //NOTE - promise原理！！！！！
    // const thenable = {
    //     then: function(resolve, reject) {
    //         reject(1)
    //     }
    // }

    // const p = new Promise((resolve, reject) => {
    //     resolve(thenable)
    // })

    // p.then(res => console.log('res', res), err => console.log('err', err))



    // const v = new Promise((resolve, reject) => {
    //     console.log('begin')
    //     resolve('then')
    // })

    // new Promise(resolve => {
    //     console.log('v对象', v)
    //     resolve(v)
    // }).then(v => {
    //     console.log(v)
    // })


    // // Promise.resolve(v).then(v => {
    // //     console.log(v, '6666')
    // // })


    // new Promise(resolve => {
    //     console.log(1)
    //     resolve()
    // }).then(() => {
    //     console.log(2)
    // }).then(() => {
    //     console.log(3)
    // }).then(() => {
    //     console.log(4)
    // })





    // class MacroTaskQueue {
    //     constructor() {
    //         this.tasks = [];
    //         this.concurrentTasks = 0;
    //         this.maxConcurrentTasks = 2;
    //     }

    //     // 执行的异步函数（需要隔开来写，与同步函数分开）
    //     // 执行完之后还要更新计数器并且再次拿任务执行
    //     async runTask(task) {
    //         await task();
    //         this.concurrentTasks--;
    //         this.checkAndRunNextTask();
    //     }

    //     // 拿任务执行的同步函数
    //     checkAndRunNextTask(data) {
    //         // 限制条件：有任务，且当前计数器小于限制值
    //         if (this.tasks.length > 0 && this.concurrentTasks < this.maxConcurrentTasks) {
    //             const task = this.tasks.shift();
    //             this.concurrentTasks++;
    //             this.runTask(task);
    //         }
    //     }

    //     // 加入任务后立刻拿任务执行
    //     add(taskFunction, data) {
    //         this.tasks.push(taskFunction);
    //         this.checkAndRunNextTask(data);
    //     }
    // }





    // const queue = new MacroTaskQueue();  

    // function delay(time) {  
    //     return new Promise(resolve => setTimeout(() => {
    //         console.log('setTimeout', time, 'tag', new Date().getTime())
    //         resolve()
    //     }, time));  
    // }

    // function addNum(time, data) {  
    //     queue.add(async () => {
    //         await delay(time);  
    //         console.log('run', data, new Date().getTime());  
    //     }, data);
    // }

    // addNum(400, '1');  
    // addNum(100, '2');  
    // addNum(1000, '3');  
    // addNum(300, '4');  
    // addNum(200, '5');

    // // 执行1和2的函数，打印2，然后执行3函数，打印1，然后执行4函数，打印4，然后执行5函数，打印5，打印3

    // async function xx() {
    //     await new Promise(() => {})
    // }





    // let isFirstLoading = null

    // function firstClick() {
    //     console.log('first click start')
    //     const fetch = new Promise((resolve, reject) => {
    //         setTimeout(() => {
    //             resolve('first click fetched')
    //         }, 1000)
    //     })
    //     isFirstLoading = new Promise((resolve, reject) => {
    //         fetch.then(res => {
    //             console.log('first click end')
    //             isFirstLoading = null
    //             resolve(res)
    //         })
    //     })
    // }

    // function secondClick() {
    //     if (isFirstLoading) {
    //         isFirstLoading.then(res => {
    //             console.log(res)
    //             console.log('second click')
    //         })
    //     } else {
    //         console.log('second click')
    //     }
    // }

    // firstClick()
    // secondClick()





    // let arr = [4, 8, 1, 0, 9, 7, 6]
    // console.log('origin arr', arr)


    // // 冒泡排序
    // function bubbleSort(arr) {

    //     for (let i = 0; i < arr.length; i++) {
    //         let isOk = true
    //         for (let j = 0; j < arr.length - i; j++) {
    //             if (arr[j+1] < arr[j]) {
    //                 isOk = false
    //                 temp = arr[j]
    //                 arr[j] = arr[j+1]
    //                 arr[j+1] = temp
    //             }
    //         }
    //         if (isOk) break
    //     }
    // }

    // // bubbleSort(arr)



    // // 选择排序
    // function maxSort(arr) {
    //     for (let i = 0; i < arr.length; i++) {
    //         let maxIndex = 0
    //         for (let j = 0; j < arr.length - i; j++) {
    //             if (arr[j] > arr[maxIndex]) {
    //                 maxIndex = j
    //             }
    //         }
    //         [arr[maxIndex], arr[arr.length - 1 - i]] = [arr[arr.length - 1 - i], arr[maxIndex]]
    //     }
    // }

    // // maxSort(arr)


    // // 插入排序
    // function insertSort(arr) {
    //     for (let i = 0; i < arr.length; i++) {
    //         let cur = arr[i]
    //         let j = i - 1
    //         while(j >= 0 && cur < arr[j] && j < arr.length) {
    //             arr[j + 1] = arr[j]
    //             j--
    //         }
    //         arr[j + 1] = cur
    //     }
    // }

    // // insertSort(arr)



    // // 归并排序
    // function mergeSort(arr) {
    //     if (arr.length <= 1) return arr
    //     let mid = Math.floor(arr.length / 2)
    //     let leftArr = arr.slice(0, mid)
    //     let rightArr = arr.slice(mid)
    //     const l = mergeSort(leftArr)
    //     const r = mergeSort(rightArr)
    //     return mergeArr(l, r)
    // }

    // function mergeArr(leftArr, rightArr) {
    //     let temp = []
    //     let point1 = 0, point2 = 0
    //     while(point1 < leftArr.length && point2 < rightArr.length) {
    //         if(leftArr[point1] > rightArr[point2]) {
    //             temp.push(rightArr[point2])
    //             point2++
    //         } else {
    //             temp.push(leftArr[point1])
    //             point1++
    //         }
    //     }
    //     if (point1 < leftArr.length) {
    //         temp.push(...leftArr.slice(point1))
    //     }
    //     if (point2 < rightArr.length) {
    //         temp.push(...rightArr.slice(point2))
    //     }
    //     return temp
    // }

    // // arr = mergeSort(arr)




    // // 快速排序
    // function fastSort(arr, left, right) {
    //     if (left >= right) return
    //     const baseIndex = findBaseIndex(arr, left, right)

    //     fastSort(arr, left, baseIndex - 1)
    //     fastSort(arr, baseIndex + 1, right)
    // }

    // function findBaseIndex(arr, left, right) {
    //     let base = arr[left]
    //     let leftPoint = left, rightPoint = right
    //     while(leftPoint < rightPoint && leftPoint < arr.length && rightPoint >= 0) {
    //         while(arr[rightPoint] >= base && leftPoint < rightPoint) {
    //             rightPoint--
    //         }
    //         arr[leftPoint] = arr[rightPoint]
    //         while(arr[leftPoint] <= base && leftPoint < rightPoint) {
    //             leftPoint++
    //         }
    //         arr[rightPoint] = arr[leftPoint]
    //     }
    //     arr[leftPoint] = base
    //     return leftPoint
    // }

    // // fastSort(arr, 0, arr.length - 1)






    // // 归并排序
    // // 构造大顶堆
    // function heapSort(arr) {
    //     const len = arr.length
    //     for (let i = Math.floor(len / 2) - 1; i >= 0; i--) {
    //         heapify(arr, len, i)
    //     }
    //     for (let i = len - 1; i >= 0; i--) {
    //         [arr[i], arr[0]] = [arr[0], arr[i]]
    //         heapify(arr, i, 0)
    //     }
    //     return arr
    // }

    // function heapify(arr, len, index) {
    //     let maxFather = index
    //     let leftChild = index * 2 + 1
    //     let rightChild = index * 2 + 2
    //     if (arr[leftChild] > arr[maxFather] && leftChild < len) {
    //         maxFather = leftChild
    //     }
    //     if (arr[rightChild] > arr[maxFather] && rightChild < len) {
    //         maxFather = rightChild
    //     }
    //     if (maxFather !== index) {
    //         [arr[maxFather], arr[index]] = [arr[index], arr[maxFather]]
    //         heapify(arr, len, maxFather)
    //     }
    // }

    // arr = heapSort(arr)

    // console.log('sorted arr', arr)


    // const obj = {
    //     a:1,
    //     b:{
    //         c:2,
    //         d:3,
    //     },
    // }

    // function isObj(val) {
    //     if (typeof val === 'object' && val !== null) return true
    //     return false
    // }

    // // 深拷贝
    // function deepCopy(obj) {
    //     if (!isObj(obj)) return
    //     const newObj = {}
    //     const keys = Object.keys(obj)
    //     for (let i = 0; i < keys.length; i++) {
    //         const curKey = keys[i]
    //         if (isObj(obj[curKey])) {
    //             const res = deepCopy(obj[curKey])
    //             newObj[curKey] = res
    //         } else {
    //             newObj[curKey] = obj[curKey]
    //         }
    //     }
    //     return newObj
    // }

    // console.log(deepCopy(obj))






    // const imgList = document.querySelectorAll('img')

    // // 懒加载图片加载原理
    // function lazyLoad(){
    //     let imgList = document.querySelectorAll("img");
    //     let windowHeight = window.innerHeight;
    //     let scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    //     for (let i = 0; i < imgList.length; i++) {
    //         if (imgList[i].offsetTop < scrollTop+windowHeight) {
    //             imgList[i].src = imgList[i].getAttribute("data-src")
    //         }
    //     }
    // }


    
    // // 更加优雅的方式
    // function isInView(dom) {
    //     const bound = dom.getBoundingClientRect()
    //     return bound.top <= window.innerHeight
    // }
    // function loadImg(dom) {
    //     dom.src = dom.getAttribute('data-src')
    // }
    // function checkAndLoad() {
    //     for (let i = 0; i < imgList.length; i++) {
    //         if (isInView(imgList[i])) {
    //             loadImg(imgList[i])
    //         }
    //     }
    // }
    // window.addEventListener('scroll', () => checkAndLoad())




    // // 优化方法：intersetsion observer
    // const imgLazyObserver = new IntersectionObserver((entries, observer) => {
    //     entries.forEach((entry) => {
    //         if (entry.isIntersecting) {
    //             const img = entry.target
    //             img.src = img.getAttribute('data-src')

    //             // 这里需要取消监控重复加载
    //             imgLazyObserver.unobserve(img)
    //         }
    //     })
    // })

    // imgList.forEach((img) => {
    //     imgLazyObserver.observe(img)
    // })











    // const arr = [1, 2, 3]
    // function task(num){
    //     return new Promise((resolve, reject) => {
    //         setTimeout(() => {
    //             console.log(num)
    //             resolve()
    //         }, 3000)
    //     })
    // }

    // task(arr[0]).then(res => {
    //     return task(arr[1])
    // }).then(res => {
    //     task(arr[2])
    // })
    


    // function back(arr, i) {
    //     if (i >= arr.length - 1) return () => task(arr[arr.length - 1])
    //     const bottomUpToTop = back(arr, i+1)
    //     const cur = i
    //     if (cur === 0) {
    //         task(arr[cur]).then(() => { bottomUpToTop()})
    //     }
    //     return () => task(arr[cur]).then(() => { bottomUpToTop()})
    // }

    // back(arr, 0)





    // function recursivePrint(arr, index = 0) {
    //     if (index >= arr.length) return
    //     if (index < arr.length - 1) {
    //         task(arr[index]).then(() => {
    //             recursivePrint(arr, index + 1);
    //         });
    //     } else {
    //         task(arr[arr.length - 1])
    //     }
    // }
    // recursivePrint(arr, 0)




    // (function loopThroughArray(arr) {
    //     let promiseChain = Promise.resolve();

    //     arr.forEach(num => {
    //         promiseChain = promiseChain.then(() => task(num));
    //     });
    // })(arr);
    

    // let promiseChain = Promise.resolve();
    // promiseChain = promiseChain.then(() => task(1));
    // promiseChain = promiseChain.then(() => task(2));
    // promiseChain = promiseChain.then(() => task(3));


    // let promiseChain = Promise.resolve()
    // arr.reduce((accum, item) => {
    //     return accum.then(() => task(item))
    // }, promiseChain)

    // promiseChain.then(() => console.log('done'))



    // Promise.resolve().then(() => task(1))
    // Promise.resolve().then(() => task(2))
    // Promise.resolve().then(() => task(3))

    



    // async function printSequentially(arr) {
    //     for (const num of arr) {
    //         await task(num);
    //     }
    // }
    // printSequentially(arr);





    // function isSupportWebp(imgPath) {
    //     const imgCheck = new Image()
    //     imgCheck.src = imgPath
    //     imgCheck.onload(() => {
    //         if (imgCheck.height > 0 && imgCheck.width > 0) {
    //             return true
    //         } else {
    //             return false
    //         }
    //     })
    //     imgCheck.onerror(() => {
    //         return false
    //     })
    // }








    // function say() {
    //     console.log('<\/script>')
    // }
    // say()


    // // 这表示默认是以async的方式进行加载的js
    // let script = document.createElement('script')
    // script.src = 'xxx.js'
    // document.head.appendChild(script)


    // // 但有的浏览器不支持async，那就需要取消异步加载的方式
    // let script = document.createElement('script')
    // script.src = 'xxx.js'
    // script.async = false
    // document.head.appendChild(script)






    // const a = '123.444aaaa334.55aaaaa'
    // const b = '10'
    // console.log(b.toString(8))

    // function sum(a, b) {
    //     a = 2;
    //     console.log(arguments[0])
    //     // 打印2
    //     arguments[0] = 3;
    //     console.log(a)
    //     // 打印3，实际上两者不是一样的变量
    //     // 只是内部有一个映射关系，把两个绑定在一起
    //     console.log(sum)
    // }
    // sum(1, 2)



    // function sum(a, b) {
    //     b = 2;
    //     console.log(arguments[1]);
    //     // 打印undefined，因为实际参数和形参不是完全一一对应，因此里面的映射关系是不存在的
    // }
    // sum(1)

    // function sum(a, b, c) {
    //     console.log(sum.length)
    //     // 打印3
    //     console.log(arguments.length)
    //     // 打印5
    // }
    // sum(2, 3, 4, 5, 6)

    // function test() {
    //     var a = b = 10
    // }
    // test()
    // console.log(window.a)
    // console.log(window.b)



    // function fn(a) {
    //     console.log(a);
    //     let a = 123;
    //     console.log(a);
    //     function a() {};
    //     console.log(a);
    //     let b = function() {};
    //     console.log(b);
    //     function d() {};
    // }
    // fn(1)


    // global = 100;
    // function fn() {
    //     console.log(global);
    //     global = 200;
    //     console.log(global);
    //     var global = 300;
    // }
    // fn();
    // var global;

    // GO = {
    //     global: undefined, // 后面执行第一行的时候变成100
    //     fn: function fn() {...},
    // }


    // AO = {
    //     global: undefined, // 后面执行里面的global赋值的时候，global变为200，以及300
    // }


    // GO = {
    //     test: function test() {...},
    //     a: undefined,
    //     c: 234, // 在执行到test函数内部的c = 234的时候，放到GO里面
    // }


    // function test() {
    //     console.log(b);
    //     if(a) {
    //         var b = 100;
    //     }
    //     console.log(b);
    //     c = 234;
    //     console.log(c);
    // }

    // var a;

    // AO = {
    //     b: undefined, 
    //     // 这里不管有没有if条件的限制，都会变量声明提升，直接忽略if条件
    //     // 但是在执行的时候，需要看if的条件是否满足，因为a此时为undefined，所以不满足条件，b后续不能被赋值
    // }

    // test();
    // a = 10;
    // console.log(c);


    

    // // 第一个全局编译
    // function bar() {
    //     return foo;
    //     foo = 100;
    //     function foo() {}
    //     var foo = 11;
    // }
    // console.log(bar());
    // // 返回 function foo() {}


    // // 第二个全局编译
    // console.log(bar());
    // function bar() {
    //     foo = 10;
    //     function foo() {}
    //     var foo = 11;
    //     return foo;
    // }
    // // 返回 11







    // function a() {
    //     function b() {
    //         var bb = 234;
    //         aa = 0;
    //     }
    //     var aa = 123;
    //     b();
    //     console.log(aa);
    // }
    // var global = 100;
    // a();






    // // 形式为: (function (){}())
    // (function (a, b, c) {
    //     return a + b + c;
    // }(1, 2, 3))

    // // 只有表达式才能被执行符号()执行
    
    // // 下面是函数声明，直接立即执行不行，语法错误
    // function test() {
    //     console.log('222')
    // }()

    // // 下面是函数表达式，直接立即执行是ok的
    // const test = function () {
    //     console.log('111')
    // }()

    // // 且执行符号执行之后，函数的名字会被忽略
    // // 相当于我函数立即执行，完了之后这个函数对象被销毁了，因此针对上面的函数，打印test是undefined

    // console.log(test)
    // // 输出为undefined





    // function test() {
    //     let arr = [];
    //     for (let i = 0; i < 10; i ++) {
    //         arr[i] = function () {
    //             console.log(i + ' ');
    //         }
    //     }
    //     return arr;
    // }
    // let myArr = test();
    // for (let i = 0; i < myArr.length; i ++) {
    //     myArr[i]();
    // }





    // function test() {
    //     let arr = [];
    //     for (var i = 0; i < 10; i ++) {
    //         arr[i] = function () {
    //             console.log(i + ' ');
    //         }
    //     }
    //     return arr;
    // }
    // let myArr = test();
    // for (let i = 0; i < myArr.length; i ++) {
    //     myArr[i]();
    // }





    // function test() {
    //     let arr = [];
    //     for (var i = 0; i < 10; i ++) {
    //         arr[i] = function (j) {
    //             console.log(j + ' ');
    //         }
    //     }
    //     return arr;
    // }
    // let myArr = test();
    // for (let i = 0; i < myArr.length; i ++) {
    //     myArr[i](i);
    // }






    // function test() {
    //     let arr = [];
    //     for (var i = 0; i < 10; i ++) {
    //         (function (j) {
    //             arr[j] = function () {
    //                 console.log(j + ' ');
    //             }
    //         }(i))
    //     }
    //     return arr;
    // }
    // let myArr = test();
    // for (let i = 0; i < myArr.length; i ++) {
    //     myArr[i]();
    // }



    // window.onload = () => {
    //     for (var i = 0; i < ulList.length; i++) {
    //         ulList[i].onclick = function () {
    //             console.log(i)
    //         }
    //     }
    // }



    // var x = 1;
    // if (function f() {}) {
    //     x += typeof f
    // }
    // console.log(x)



    // function Car() {
    //     this.name = 'BMW';
    // }

    // const car1 = new Car();




    // function Person(name, height) {
    //     const that = {};
    //     that.name = name;
    //     that.height = height;
    //     return that;
    // }

    // const person1 = new Person('1', 180)
    // const person2 = new Person('2', 185)



    // const test = 'abc'
    // test.name = 'ss'
    // console.log(test.name)





    // Person.prototype.name = 'abc';
    // function Person() {
    //     // 第一步：
    //     let this = {
    //         // __proto__相当于连接儿子和父亲
    //         // 当儿子本身没有一个属性或方法的时候，访问__proto__来访问父亲
    //         __proto__: Person.prototype,
    //     }

    // }
    // const person1 = new Person()



    // console.log(Person.prototype.__proto__ === Object.prototype)
    // Person.prototype.name = 'sunny';
    // function Person() {
    // }

    // const person1 = new Person();
    // console.log(person1)

    // Person.prototype = {
    //     name: 'cherry',
    // }

    // console.log(person1.name)




    // let obj = {name: 'sunny'};
    // let obj1 = obj;
    // obj = {name: 'cherry'};

    // console.log(obj1)





    // function Father() {
    //     this.money = {
    //         card1: '100',
    //     };
    // }
    // const f1 = new Father();

    // Son.prototype = f1;
    // function Son() {

    // }
    // const s1 = new Son();

    // s1.money.card1 = '200'
    // console.log(f1.money)




    // function Father() {}
    // const f1 = Object.create(Father.prototype)
    


    // let num = 123;
    // num.toString();
    // // 打印‘123’

    // // 内部发生了什么
    // // new Number(123).toString()
    // // 调用的是Number.prototype.toString()，而不是Object.prototype.toString()

    // let obj = {}
    // obj.toString()
    // // 返回的是[object object]



    // function Person(name, age) {
    //     this.name = name;
    //     this.age = age;
    // }
    // const person1 = new Person('z', 20);
    // let obj = {}
    // Person.call(obj, 'y', 30)
    // console.log(obj)


    // function Person(name, age) {
    //     this.name = name;
    //     this.age = age;
    // }


    // function Student(name, age, grade) {
    //     Person.call(this, name, age)
    //     // 上面相当于给Student(this)加上了name和age的属性（Person的属性）
    //     this.grade = grade
    // }

    // const s1 = new Student('z', 20, 3)








    // function Wheel(style, wheelSize) {
    //     this.style = style;
    //     this.wheelSize = wheelSize;
    // }

    // function Sit(sitColor) {
    //     this.sitColor = sitColor;
    // }

    // function Model(height, width, len) {
    //     this.height = height;
    //     this.width = width;
    //     this.len = len;
    // }

    // function Car(style, wheelSize, sitColor, height, width, len) {
    //     Wheel.call(this, style, wheelSize);
    //     Sit.call(this, sitColor);
    //     Model.call(this, height, width, len);

    //     // apply传递的参数是数组，但是顺序也要一样
    //     // Wheel.apply(this, [style, wheelSize]);
    //     // Sit.apply(this, [sitColor]);
    //     // Model.apply(this, [height, width, len]);
    // }

    // const myCar = new Car('z', 20, 'green', 200, 300, 400);
    // console.log(myCar)





    // Grand.prototype.lastName = 'll';
    // function Grand() {
        
    // }
    // const grand = new Grand();

    // Father.prototype = grand;
    // function Father() {
    //     this.name = 'zz'
    // }
    // const father = new Father();

    // // Son 的本意是继承Grand的lastName，但是他必须要经过father这一层
    // // 有点像props的传递
    // Son.prototype = father;
    // function Son() {

    // }
    // const son = new Son;









    // Father.prototype.lastName = 'zz';
    // function Father(name) {
    //     this.name = name
    // }
    // function Son() {
    // }
    // Son.prototype = Father.prototype;
    // // 相当于Father的原型给了Father和Son
    // // Father和Son的祖先是同一个对象


    // // 更好的写法：封装一个方法实现共有对象的继承
    // // function inherit(target, origin) {
    // //     target.prototype = origin.prototype;
    // // }
    // inherit(Son, Father);

    // // // 不能修改自己的原型，因为这时Son.prototype指向的是同一个房间
    // // Son.prototype.gender = 'female';

    // const father = new Father();
    // const son = new Son();
    // // console.log(father.gender)
    // // // 打印female




    // // 加一个中间层
    // // 一方面，中间层的原型和origin原型一致
    // // 另一方面，中间层本身是target的原型
    // // 修改target的原型不会改father的原型

    // function inherit(target, origin) {
    //     function Middle() {}
    //     Middle.prototype = origin.prototype
    //     target.prototype = new Middle()
    // }

    // // 但是这样有一个问题
    // // son 的constructor变成了Father() {}
    // // son.__proto__(找此实例的构造函数的原型即Son.prototype)为new Middle()
    // // new Middle().__proto__(找此实例的构造函数的原型即Middle.prototype)为Father.prototype
    // // Father.prototype.__proto__指向Object.prototype(Father.prototype是对象，其构造函数肯定是Object)，这时到头了
    // // 因此找Father.prototype.constructor，为Father() {}
    // function inherit(target, origin) {
        // function Middle() {};
        // Middle.prototype = origin.prototype;
        // target.prototype = new Middle();
        
    //     // 改一下constructor
    //     target.prototype.constructor = target;

    //     // 记录一下真正继承的目标函数father.prototype
    //     target.prototype.super = origin.prototype

    //     // 如果想要继承father本身的属性
    //     target.prototype.super = function (name) {
    //         return new origin(name)
    //     }
    // }


    // // Middle本来就不是一个重要的变量，直接变成私有变量
    // const inherit = (function () {
    //     const Middle = function() {};
    //     return function(target, origin) {
    //         Middle.prototype = origin.prototype;
    //         target.prototype = new Middle();

    //         target.prototype.constructor = target;
    //         target.prototype.super = origin.prototype;
    //     }
    // }())



    


    // function Person() {
    //     const name = 'zzz'
    //     this.getPrivateName = function() {
    //         console.log(name)
    //     }
    // }
    // const person = new Person()
    // person.getPrivateName()
    // console.log(person.name)





    // // 父亲对象
    // function Father(gender) {
    //     this.gender = gender
    // }
    // Father.prototype.getGender = function() {
    //     console.log('this.gender', this.gender)
    // }

    // // 儿子对象
    // function Son(name, age) {
    //     this.name = name
    //     this.age = age
    // }

    // // 儿子继承父亲的对象
    // // 首先继承父亲的方法
    // Son.prototype = Object.create(Father.prototype)
    // // 改一下constructor
    // Son.prototype = Object.create(Father.prototype, {
    //     constructor: {
    //         value: Son,
    //         writable: true,
    //         configurable: true,
    //         enumerable: false,
    //     }
    // })

    // // 然后需要继承父亲的属性（因为属性要传递参数，必须用一个函数来实现）
    // Son.prototype.super = function(gender) {
    //     return new Father(gender)
    // }

    // // 儿子对象原本的自己的方法加上
    // Son.prototype.getName = function() {
    //     console.log(this.name)
    // }
    // Son.prototype.msg = function() {
    //     console.log('msg', this.name, this.age)
    // }

    // // 儿子的实例
    // const son = new Son('ming',25)

    // // 下面返回 this.gender female
    // son.super('female').getGender()

    // // 下面返回undefined，因为儿子实例本身是没有父亲的属性的
    // console.log(son.gender)






    // const name = '456'
    // const init = (function () {
    //     const name = '123';
    //     function callName() {
    //         console.log(name);
    //     }
    //     return function() {
    //         callName();
    //     }
    // }())
    // init()
    // // 打印的是123，而不是456




    // const actions = {
    //     smoke: function () {
    //         console.log('smoking');
    //         return this;
    //     },
    //     drink: function () {
    //         console.log('drinking');
    //         return this;
    //     },
    //     perm: function () {
    //         console.log('perming');
    //         return this;
    //     },
    // };

    // actions.smoke().drink().perm().smoke()




    // const obj = {
    //     num1: {name: '111'},
    //     num2: {name: '222'},
    //     num3: {name: '333'},
    //     num4: {name: '444'},
    //     getNum: function(num) {
    //         console.log(this[`num${num}`])
    //     }
    // }
    // // obj.getNum(2)


    // for (let i in obj) {
    //     console.log(obj[i])
    // }



    // var name = '123';
    // const a = {
    //     name: '111',
    //     getName: function() {
    //         console.log(this.name);
    //     }
    // }
    // const fn = a.getName;
    // fn(); // 打印123
    // a.getName(); // 打印111

    // const b = {
    //     name: '222',
    //     getName: function(fn) {
    //         fn()
    //     }
    // }
    // b.getName(a.getName) // 打印123
    // b.getName = a.getName
    // b.getName() // 打印222



    // // 求n的阶层，用递归！
    // const num = (function (n) {
    //     if (n === 1) {
    //         return 1;
    //     }
    //     // 这里外部函数没有名字，用arguments.callee来代替当前外部函数
    //     return n * arguments.callee(n - 1)

    // }(5));


    // function test() {
    //     demo()
    // }
    // function demo() {
    //     console.log(demo.caller)
    // }
    // test()



    // var foo = 123;
    // function print() {
    //     this.foo = 234;
    //     console.log(foo);
    // }
    // print(); // 打印234
    // new print() // 打印123


    // const arr = [5, 2, 1, 8, 4, 10, 9]
    // arr.sort(() => a - b)
    // 底层实现：递归思路的比较顺序
    // 第一层：5和2对比，5和1对比...5和9对比
    // 第二层：2和1比较，2和8比较...2和9比较
    // ...

    // 规则：
    // 回调函数返回值大于0，后面的数在前面（大后前）
    // 回调函数返回值小于0，前面的数在前面（小前前）
    // 返回值为0，不动



    // arr.sort(() => {
    //     return Math.random() - 0.5
    // })




    // const obj = {
    //     '0': 'a',
    //     '1': 'b',
    //     '2': 'c',
    //     '3': 'd',
    //     'length': 4,
    //     'push': Array.prototype.push,
    //     'splice': Array.prototype.splice,
    // }
    // // 类数组要求的：属性要为索引，要有length属性
    // // 一旦加上splice属性，这个对象打印出来长成数组那样

    // // 在这里，push方法的底层实现如下：
    // Array.prototype.push = function(target) {
    //     obj[obj.length] = target;
    //     obj.length ++;
    // }


    // const obj1 = {
    //     '2': 'c',
    //     '3': 'd',
    //     'length': 2,
    //     'push': Array.prototype.push,
    //     'splice': Array.prototype.splice,
    // }
    // obj1.push('a');
    // obj1.push('b');
    // // 得到的obj1是
    // obj1 = {
    //     '2': 'a',
    //     '3': 'b',
    //     'length': 4,
    //     ...
    // }



    // const obj = {
    //     department1: {
    //         zyl: {
    //             name: 'zyl',
    //             age: 123,
    //         },
    //         zzc: {
    //             name: 'zzc',
    //             age: 234,
    //         }
    //     },
    //     department2: {
    //         zxq: {
    //             name: 'zxq',
    //             age: 456,
    //         }
    //     }
    // }

    // with(obj.department1.zyl) {
    //     console.log(name)
    // }
    // with(obj.department1.zzc) {
    //     console.log(name)
    // }



    

    // 'use strict';
    // console.log(this) // 全局this打印window
    // function test() {
    //     console.log(this) // 局部this，没有被定义，打印undefined
    // }
    // test()






    // console.log(
    //     HTMLHeadElement.prototype.__proto__ === HTMLElement.prototype
    // )



    // const date1 = new Date().getTime()
    // for (let i = 0; i < 10000000; i++) {}
    // const data2 = new  Date().getTime()
    // console.log(data2 - date1)



    // let firstTime = new Date().getTime();
    // const timer1 = setInterval(function () {}, 1000)
    // const timer2 = setTimeout(function () {}, 1000)
    // console.log(timer1, timer2)


    // // 100000000 ——> 100.000.000
    // const reg = /(?=(\B)(\d{3})+$)/g;
    // const str = '100000000';
    // console.log(str.replace(reg, '.'));



    
    // function solution(numbers) {
    //     let res = 0;
    //     function back(outArrIndex, pathCount, pathSum) {
    //         if (pathCount === numbers.length) {
    //             if (pathSum % 2 === 0) {
    //                 res ++;
    //             }
    //             return;
    //         }
    //         const curLayer = numbers[outArrIndex] + '';
    //         for (let i = 0; i < curLayer.length; i++) {
    //             const newPathSum = pathSum + (+curLayer[i]);
    //             back(outArrIndex + 1, pathCount + 1, newPathSum)
    //         }
    //     }
    //     back(0, 0, 0)
    //     return res;
    // }

    // console.log(solution([123, 456, 789]));





    // function solution(n, max, array) {
    //     const map = {};
    //     let slow = 0, fast = 0;
    //     let leTwo = [], geThree = [];
    //     let sortedArray = array.sort((a, b) => b - a);

    //     while (fast <= n) {
    //         if (sortedArray[fast] !== sortedArray[slow]) {
    //             if (fast - slow >= 3) {
    //                 geThree.push(sortedArray[slow]);
    //                 leTwo.push(sortedArray[slow]);
    //             } else if (fast - slow === 2) {
    //                 leTwo.push(sortedArray[slow]);
    //             }
    //             slow = fast;
    //         }
    //         fast ++;
    //     }

    //     const all = [geThree, leTwo];




    //     let res = [], curA = 0, curB = 0;
    //     function back(index, cardsCount, a, b) {
    //         if (cardsCount === 2) {
    //             if ((a * 3 + b * 2) <= max) {
    //                 a = a === 1 ? 14 : a
    //                 b = b === 1 ? 14 : b
    //                 if (a > curA) {
    //                     const a1 = a === 14 ? 1 : a
    //                     const b1 = b === 14 ? 1 : b
    //                     res = [a1, b1];
    //                     curA = a;
    //                     curB = b;
    //                 } else if (a === curA && b > curB) {
    //                     const a1 = a === 14 ? 1 : a
    //                     const b1 = b === 14 ? 1 : b
    //                     res = [a1, b1];
    //                     curB = b;
    //                 }
    //             }
    //             return;
    //         }
    //         const curLayer = all[index];
    //         for (let i = 0; i < curLayer.length; i++) {
    //             let curLayerNum = curLayer[i];
    //             if ((index === 0 && curLayerNum === b) || (index === 1 && curLayerNum === a)) {
    //                 continue;
    //             }
    //             if (index === 0) {
    //                 back(index + 1, cardsCount + 1, curLayerNum, b);
    //             } else {
    //                 back(index + 1, cardsCount + 1, a, curLayerNum);
    //             }
    //         }
    //     }
    //     back(0, 0, null, null)
    //     return res.length > 0 ? res : [0, 0];
    // }

    // function main() {
    //     // Add your test cases here
    //     // console.log(JSON.stringify(solution(9, 34, [6, 6, 6, 8, 8, 8, 5, 5, 1])) === JSON.stringify([8, 5]));
    //     // console.log(JSON.stringify(solution(9, 37, [9, 9, 9, 9, 6, 6, 6, 6, 13])) === JSON.stringify([6, 9]));
    //     // console.log(JSON.stringify(solution(26, 99, [10,6,3,1,1,1,4,11,4,13,3,7,13,4,10,1,7,11,12,10,3,4,7,9,3,12])) === JSON.stringify([1, 13]));
    //     console.log(JSON.stringify(solution(31, 42, [3,3,11,12,12,2,13,5,13,1,13,8,8,1,8,13,12,9,2,11,3,5,8,11,1,11,1,5,4,2,5])) === JSON.stringify([1, 13]));
    // }

    // main();






    // function solution(a, b) {
    //     const strA = a + ''
    //     const strB = b + ''
    //     console.log(strA, strB)
    //     let res = strA + b
    //     if (b > a && strA.length > 1) return Number(strB + strA)
    //     for (let i = 0; i < strA.length; i++) {
    //         if ((+strB[0]) > (+strA[i])) {
    //             res = strA.substring(0, i) + b + strA.substring(i, strA.length)
    //             return Number(res)
    //         }
    //     }
    //     return Number(res)
    // }


    // function main() {
    //     // console.log(solution(76543, 4) === 765443);
    //     // console.log(solution(1, 0) === 10);
    //     // console.log(solution(44, 5) === 544);
    //     // console.log(solution(666, 6) === 6666);
    //     console.log(solution(12, 15) === 1512);
    // }

    // main();






    
    // function solution(n, k, data) {
    //     let curFood = 0;
    //     let totalCost = 0;
    //     for (let i = 0; i < data.length; i++) {
    //         console.log(`====第${i}天=====`)
    //         console.log('剩下的食物--', curFood)
    //         let futureNeedInBagFood = Math.min(k, n - i - 1); // 不算今天的
    //         let minPrice = Math.min(...data.slice(i, i + futureNeedInBagFood + 1))
    //         console.log('futureNeedInBagFood', futureNeedInBagFood)
    //         console.log('minPrice', minPrice)
    //         if (data[i] > minPrice) {
    //             if (curFood < 1) {
    //                 totalCost += data[i];
    //                 curFood ++;
    //             }
    //         } else {
    //             if (curFood < futureNeedInBagFood + 1) {
    //                 totalCost += minPrice * (futureNeedInBagFood - (curFood - 1))
    //                 curFood = futureNeedInBagFood + 1;
    //             }
    //         }
    //         console.log('花了多少钱---',totalCost)
    //         curFood --;
    //     }

    //     return totalCost;
    // }

    // function main() {
    //     // Add your test cases here
    //     console.log(solution(5, 2, [1, 2, 3, 3, 2]) === 9);
    //     console.log(solution(6, 3, [4, 1, 5, 2, 1, 3]) === 9);
    //     console.log(solution(4, 1, [3, 2, 4, 1]) === 10);
    // }

    // main();






    // function solution(n, H, A, h, a) {
    //     const mons = {
    //         length: h.length,
    //         sort: Array.prototype.sort,
    //     }
    //     for (let i = 0; i < h.length; i++) {
    //         mons[i] = {
    //             a: a[i],
    //             h: h[i],
    //             sum: a[i] + h[i],
    //             originIndex: i,
    //         }
    //     }
    //     console.log(mons)
    //     mons.sort((a, b) => b.sum - a.sum)

    //     let playerH = H, playerA = A;
    //     let res = 0;
    //     for (let j = 0; j < mons.length; j++) {
    //         const mon = mons[j];
    //         console.log('curMon', mon)
    //         if (playerA > mon.a && playerH > mon.h) {
    //             console.log('feat mon!----')
    //             res ++;
    //             playerA = mon.a;
    //             playerH = mon.h;
    //             console.log('after feat---a-', playerA, '--h--', playerH)
    //         }
    //     }
    //     console.log('res', res)
    //     return res;
    // }

    // function main() {
    //     console.log(solution(3, 4, 5, [1, 2, 3], [3, 2, 1]) === 1);
    //     console.log(solution(5, 10, 10, [6, 9, 12, 4, 7], [8, 9, 10, 2, 5]) === 2);
    //     console.log(solution(4, 20, 25, [10, 15, 18, 22], [12, 18, 20, 26]) === 3);
    // }

    // main();







    // function solution(values) {
    //     let path = [];
    //     let res = [];
    //     function back(startIndex) {
    //         if (path.length === 2) {
    //             res.push(path.slice());
    //             return;
    //         }
    //         for (let i = startIndex; i < values.length; i++) {
                
    //             path.push({
    //                 num: values[i],
    //                 originIndex: i,
    //             });
    //             back(i + 1);
    //             path.pop();
    //         }
    //     }
    //     back(0)
    //     console.log(res)
    //     let max = 0
    //     for (let j = 0; j < res.length; j++) {
    //         const curGroup = res[j]
    //         const indexA = curGroup[0].originIndex;
    //         const indexB = curGroup[1].originIndex;
    //         const valueA = curGroup[0].num;
    //         const valueB = curGroup[1].num;
    //         const score = valueA + valueB - Math.abs(indexA - indexB);
    //         if (score > max) {
    //             max = score
    //         }
    //     }
    //     console.log('maxscore', max)
    //     return max
    // }

    // function main() {
    //     console.log(solution([8, 3, 5, 5, 6]) === 11 ? 1 : 0);
    //     console.log(solution([10, 4, 8, 7]) === 16 ? 1 : 0);
    //     console.log(solution([1, 2, 3, 4, 5]) === 8 ? 1 : 0);
    // }

    // main();






    

    // function solution(S, T) {
    //     let p1 = 0, p2 = 0;
    //     let changeIndex = 0;
        
    //     while(p1 < S.length && p2 < T.length) {
    //         if (S[p1] !== T[p2]) {
    //             changeIndex ++;
    //         }
    //         p1 ++;
    //         p2 ++;
    //     }
    //     if (S.length > T.length) {
    //         if (changeIndex === 0) {
    //             changeIndex = S.length - T.length;
    //         } else {
    //             changeIndex += S.length - T.length;
    //         }

    //     }
    //     console.log('changeIndex', changeIndex)
    //     return changeIndex
    // }

    // function main() {
    //     console.log(solution("aba", "abb") === 1);
    //     console.log(solution("abcd", "efg") === 4);
    //     console.log(solution("xyz", "xy") === 1);
    //     console.log(solution("hello", "helloworld") === 0);
    //     console.log(solution("same", "same") === 0);
    // }

    // main();








    // function solution(n, a) {
    //     let res = [], path = [];
    //     const aLen = a.length;
    //     function back(startIndex) {
    //         if (path.length === 2) {
    //             res.push(path.slice());
    //             return;
    //         }
    //         for (let i = startIndex; i < aLen; i++) {
    //             path.push({
    //                 num: a[i],
    //                 originIndex: i,
    //             });
    //             back(i + 1);
    //             path.pop();
    //         }
    //     }
    //     back(0)

    //     let max = 0;
    //     for (let j = 0; j < res.length; j ++) {
    //         const curGroup = res[j];
    //         const a = curGroup[0];
    //         const b = curGroup[1];
    //         let dist1, dist2;
    //         let minDist;
    //         if (a.originIndex > b.originIndex) {
    //             dist2 = aLen - a.originIndex + b.originIndex;
    //             dist1 = a.originIndex - b.originIndex;
    //             minDist = Math.min(dist1, dist2);
    //         } else {
    //             dist2 = aLen - b.originIndex + a.originIndex;
    //             dist1 = b.originIndex - a.originIndex;
    //             minDist = Math.min(dist1, dist2);
    //         }

    //         const score = (a.num + b.num) * minDist;
    //         if (score > max) {
    //             max = score;
    //         }
            
    //     }
    //     return max;
    // }

    // function main() {
    //     console.log(solution(3, [1, 2, 3]) === 5);
    //     console.log(solution(4, [4, 1, 2, 3]) === 12);
    //     console.log(solution(5, [1, 5, 3, 7, 2]) === 24);
    // }

    // main();


    



    // function solution(S) {
    //     let map = {};
    //     let doubleCount = 0;
    //     for (let i = 0; i < S.length; i++) {
    //         const curChar = S[i];
    //         if (map[curChar]) {
    //             map[curChar] ++;
    //             if (map[curChar] % 2 === 0) {
    //                 doubleCount ++;
    //             }
    //         } else {
    //             map[curChar] = 1;
    //         }
    //     }
    //     return doubleCount
    // }

    // function main() {
    //     console.log(solution("abab") === 2);
    //     console.log(solution("aaaa") === 2);
    //     console.log(solution("abcabc") === 3);
    // }

    // main();




    // function solution(n, a) {
    //     let res = [];
    //     function back(arr) {
    //         if (arr.length <= 0) return;
    //         const listen = arr.shift();
    //         res.push(listen);
    //         if (arr.length > 0) {
    //             const temp = arr.shift();
    //             arr.push(temp);
    //             back(arr);
    //         }
    //     }
    //     back(a)
    //     return res
    // }

    // function main() {
    //     console.log(JSON.stringify(solution(5, [5, 3, 2, 1, 4])) === JSON.stringify([5, 2, 4, 1, 3]));
    //     console.log(JSON.stringify(solution(4, [4, 1, 3, 2])) === JSON.stringify([4, 3, 1, 2]));
    //     console.log(JSON.stringify(solution(6, [1, 2, 3, 4, 5, 6])) === JSON.stringify([1, 3, 5, 2, 6, 4]));
    // }

    // main();







    // function solution(word) {
    //     let arr = word.split('');
    //     for (let i = 0; i < arr.length; i++) {
    //         const curStr = arr[i];
    //         if (!+curStr) {
    //             arr[i] = ' ';
    //         }
    //     }

    //     const reg = /\s{2,}/g
    //     const str = arr.join('').trim().replace(reg, ' ')

    //     if (!str) {
    //         return 0
    //     }
    //     const arr2 = str.split(' ').sort((a, b) => a - b);
        
    //     let slow = 0, fast = 0;
    //     while(fast < arr2.length) {
    //         if (+arr2[fast] !== +arr2[slow]) {
    //             slow++;
    //             arr2[slow] = arr2[fast];
    //         }
    //         fast++;
    //     }
    //     return slow + 1;

    // }

    // function main() {
    //     console.log(solution("a123bc34d8ef34") === 3);
    //     console.log(solution("t1234c23456") === 2);
    //     console.log(solution("a1b01c001d4") === 2);
    //     console.log(solution("m") === 0);
    //     console.log(solution("7g06td8xapw6r1d25z1ick") === 5);
    // }

    // main();










    // function solution(s, a, m, k) {

    //     const newArr = a.map((item, index) => ({
    //         price: item,
    //         mesh: s[index],
    //     })).sort((a, b) => a.price - b.price);

    //     let meshDist = 0;
    //     let pay = 0;
    //     let allDist = k;

    //     // 处理没法点菜的情况
    //     const reg = /1/g
    //     const matched = s.match(reg)
    //     if (a.length - matched.length < k - m || a.length < k) {
    //         return -1
    //     }

    //     for (let i = 0; i < newArr.length && allDist > 0; i++) {
    //         const curDish = newArr[i]
    //         if (curDish.mesh === '1') {
    //             meshDist ++;
    //             if (meshDist > m) {
    //                 meshDist --;
    //                 continue;
    //             }
    //         }
    //         pay += curDish.price;
    //         allDist --;
    //     }
    //     return pay
    // }

    // function main() {
    //     console.log(solution("001", [10, 20, 30], 1, 2) === 30);
    //     console.log(solution("111", [10, 20, 30], 1, 2) === -1);
    //     console.log(solution("0101", [5, 15, 10, 20], 2, 3) === 30);
    //     console.log(solution("1", [6], 16, 16) === -1);
    // }

    // main();








    // function solution(n, a, b) {
    //     let outArr = [];
    //     for (let j = 0; j < a.length; j++) {
    //         outArr.push([a[j], b[j]]);
    //     }

    //     let res = 0;
    //     function back(outArrIndex, pathSum, pathCount) {
    //         if (pathCount === outArr.length) {
    //             if (pathSum % 3 === 0) {
    //                 res ++;
    //             }
    //             return;
    //         }
    //         const curLayer = outArr[outArrIndex];
    //         for (let i = 0; i < curLayer.length; i++) {
    //             const newPathSum = pathSum + curLayer[i];
    //             back(outArrIndex + 1, newPathSum, pathCount + 1);
    //         }
    //     }
    //     back(0, 0, 0);
    //     return res;
    // }

    // function main() {
    //     console.log(solution(3, [1, 2, 3], [2, 3, 2]) === 3);
    //     console.log(solution(4, [3, 1, 2, 4], [1, 2, 3, 1]) === 6);
    //     console.log(solution(5, [1, 2, 3, 4, 5], [1, 2, 3, 4, 5]) === 32);
    // }

    // main();








    // function solution(n, u) {
    //     u.sort((a, b) => a - b);
    //     let fast = 0, slow = 0;
    //     let notRepeat = 0;
    //     while(fast < u.length) {
    //         if(u[fast] !== u[slow]) {
    //             slow++;
    //             u[slow] = u[fast];
    //             notRepeat++;
    //         }
    //         fast++;
    //     }

    //     if (slow === 0) {
    //         return 0
    //     }

    //     if (u.length % 2 === 0 && notRepeat % 2 !== 0) {
    //         return u.length / (notRepeat + 1)
    //     }
    //     if (u.length % 2 !== 0 && notRepeat % 2 === 0) {
    //         return Math.ceil(u.length / 2)
    //     }

    // }

    // function main() {
    //     console.log(solution(5, [1, 2, 3, 1, 2]) === 3);
    //     console.log(solution(4, [100000, 100000, 100000, 100000]) === 0);
    //     console.log(solution(6, [1, 1, 1, 2, 2, 2]) === 3);
    // }

    // main();









    // function solution(n, a) {
    //     let res = 0;
    //     for (let i = 0; i < a.length; i++) {
    //         const curNum = a[i];
    //         const curNumStr = curNum + '';
    //         const reg = /0/g
    //         const matched = curNumStr.match(reg)
    //         if (matched) {
    //             res += curNumStr.length - matched.length;
    //         } else {
    //             res += curNumStr.length;
    //         }

    //     }
    //     return res
    // }





    // function main() {
    //     console.log(solution(5, [10, 13, 22, 100, 30]) === 7);
    //     console.log(solution(3, [5, 50, 505]) === 4);
    //     console.log(solution(4, [1000, 1, 10, 100]) === 4);
    // }

    // main();



    // function bubbleSort(arr) {
    //     for (let i = 0; i < arr.length; i++) {
    //         let isOk = true
    //         for (let j = 0; j < arr.length - i; j++) {
    //             if (arr[j] > arr[j + 1]) {
    //                 isOk = false
    //                 let temp = arr[j + 1];
    //                 arr[j + 1] = arr[j];
    //                 arr[j] = temp;
    //             }
    //         }
    //         if (isOk) break
    //     }
    // }


    // function selectSort(arr) {
    //     for (let i = 0; i < arr.length; i++) {
    //         let maxIndex = 0;
    //         for (let j = 0; j < arr.length - i; j++) {
    //             if (arr[j] > arr[maxIndex]) {
    //                 maxIndex = j;
    //             }
    //         }
    //         [arr[maxIndex], arr[arr.length - i - 1]] = [arr[arr.length - 1], arr[maxIndex]]
    //     }
    // }


    // function insertSort(arr) {
    //     for (let i = 0; i < arr.length; i++) {
    //         let curNum = arr[i];
    //         let j = i - 1;
    //         while(j >= 0 && arr[j] > curNum) {
    //             arr[j + 1] = arr[j];
    //             j--;
    //         }
    //         arr[j + 1] = curNum;
    //     }
    // }




    // function mergeSort(arr) {
    //     let len = arr.length;
    //     if (len <= 1) return arr;
    //     const mid = Math.floor(len / 2);
    //     const rightArr = arr.slice(mid);
    //     const leftArr = arr.slice(0, mid);
    //     const r = mergeSort(rightArr);
    //     const l = mergeSort(leftArr);
    //     return merge(r, l)
    // }


    // function merge(right, left) {
    //     let p1 = 0, p2 = 0;
    //     let temp = [];
    //     while(p1 < right.length && p2 < left.length) {
    //         if (right[p1] < left[p2]) {
    //             temp.push(right[p1]);
    //             p1++;
    //         } else {
    //             temp.push(left[p2]);
    //             p2++;
    //         }
    //     }
    //     if (p1 < right.length) {
    //         temp.push(...right.slice(p1))
    //     }
    //     if (p2 < left.length) {
    //         temp.push(...left.slice(p2))
    //     }
    //     return temp
    // }



    // function fastSort(arr, left, right) {
    //     if (left >= right) return
    //     const baseIndex = findBaseIndex(arr, left, right)

    //     fastSort(arr, left, baseIndex - 1)
    //     fastSort(arr, baseIndex + 1, right)
    // }

    // function findBaseIndex(arr, left, right) {
    //     let base = arr[left]
    //     let leftPoint = left, rightPoint = right
    //     while(leftPoint < rightPoint && leftPoint < arr.length && rightPoint >= 0) {
    //         while(arr[rightPoint] >= base && leftPoint < rightPoint) {
    //             rightPoint--
    //         }
    //         arr[leftPoint] = arr[rightPoint]
    //         while(arr[leftPoint] <= base && leftPoint < rightPoint) {
    //             leftPoint++
    //         }
    //         arr[rightPoint] = arr[leftPoint]
    //     }
    //     arr[leftPoint] = base
    //     return leftPoint
    // }

    // // fastSort(arr, 0, arr.length - 1)







    // // 构造大顶堆
    // function heapSort(arr) {
    //     const len = arr.length
    //     for (let i = Math.floor(len / 2) - 1; i >= 0; i--) {
    //         heapify(arr, len, i)
    //     }
    //     for (let i = len - 1; i >= 0; i--) {
    //         [arr[i], arr[0]] = [arr[0], arr[i]]
    //         heapify(arr, i, 0)
    //     }
    //     return arr
    // }

    // function heapify(arr, len, index) {
    //     let maxFather = index
    //     let leftChild = index * 2 + 1
    //     let rightChild = index * 2 + 2
    //     if (arr[leftChild] > arr[maxFather] && leftChild < len) {
    //         maxFather = leftChild
    //     }
    //     if (arr[rightChild] > arr[maxFather] && rightChild < len) {
    //         maxFather = rightChild
    //     }
    //     if (maxFather !== index) {
    //         [arr[maxFather], arr[index]] = [arr[index], arr[maxFather]]
    //         heapify(arr, len, maxFather)
    //     }
    // }

    // arr = heapSort(arr)


    // // 深拷贝
    // function deepCopy(obj) {
    //     if (!isObj(obj)) return
    //     const newObj = {}
    //     const keys = Object.keys(obj)
    //     for (let i = 0; i < keys.length; i++) {
    //         const curKey = keys[i]
    //         if (isObj(obj[curKey])) {
    //             const res = deepCopy(obj[curKey])
    //             newObj[curKey] = res
    //         } else {
    //             newObj[curKey] = obj[curKey]
    //         }
    //     }
    //     return newObj
    // }

    // console.log(deepCopy(obj))

    

    // function promiseAll(promises) {
    //     return new Promise((resolve, reject) => {
    //         if (!Array.isArray(promises)) return new Error('not array')
    //         let result = []
    //         let resCount = 0
    //         for (let i = 0; i < promises.length; i++) {
    //             Promise.resolve(promises[i]).then(res => {
    //                 resCount++
    //                 result[i] = res
    //                 if (resCount === promises.length) {
    //                     return resolve(result)
    //                 }
    //             }).catch(err => {
    //                 return reject(err)
    //             })
    //         }
    //     })
    // }




    // function solution(arr, k) {
    //     let map = {};
    //     let isOver = false;
    //     let res = null;

    //     function win(map, win) {
    //         if (!map[win]) {
    //             map[win] = {
    //                 lastTime: 'win',
    //                 num: 1,
    //                 continueCount: 1,
    //             }
    //         } else {
    //             map[win].num += 1;
    //             if (map[win].lastTime === 'win') {
    //                 map[win].continueCount += 1;
    //             }
    //         }
    //     }

    //     function back(arr) {
    //         for (let i = 0; i < arr.length; i++) {
    //             let f = arr[0]
    //             let s = arr[1]
    //             if (f > s) {
    //                 arr.splice(1, 1)
    //                 arr.push(s)
    //                 win(map, f)
    //                 if (map[f].continueCount === k) {
    //                     isOver = true;
    //                     res = f;
    //                     return
    //                 }
    //             } else {
    //                 arr[0] = s
    //                 arr.splice(1, 1)
    //                 arr.push(f)
    //                 win(map, s)
    //                 if (map[s].continueCount === k) {
    //                     isOver = true;
    //                     res = s;
    //                     return
    //                 }
    //             }
    //         }
    //         if (!isOver) back(arr);
    //     }
    //     back(arr)
    //     return res;
    // }

    // function main() {
    //     console.log(solution([2, 1, 3, 5, 4, 6, 7, 9], 2) === 5);
    //     console.log(solution([3, 2, 1, 4], 10) === 4);
    //     console.log(solution([1, 9, 8, 7, 6, 5, 4, 3, 2, 11], 7) === 9);
    // }

    // main();







    // function solution(nums) {
    //     let map = {};
    //     let m = 0;
    //     for (let i = 0; i < nums.length; i++) {
    //         const curNum = nums[i]
    //         if (map[curNum]) {
    //             map[curNum] += 1;
    //         } else {
    //             map[curNum] = 1;
    //         }
    //         if (map[curNum] % 5 === 0) {
    //             m += 1;
    //         }
    //     }
    //     if (nums.length % m === 0) {
    //         return "True";
    //     } else {
    //         return "False";
    //     }
    // }

    // function main() {
    // // You can add more test cases here
    // console.log(solution([1, 3, 4, 5, 6, 5, 4]) === "False");
    // console.log(solution([1, 1, 1, 1, 2, 1, 2, 2, 2, 2]) === "True");
    // console.log(solution([11, 45, 49, 37, 45, 38, 3, 47, 35, 49, 26, 16, 24, 4, 45, 39, 28, 26, 14, 22, 4, 49, 18, 4, 4, 26, 47, 14, 1, 21, 9, 26, 17, 12, 44, 28, 24, 24, 10, 31, 33, 32, 23, 41, 41, 19, 17, 24, 28, 46, 28, 4, 18, 23, 48, 45, 7, 21, 12, 40, 2, 19, 19, 28, 32, 6, 27, 43, 6, 18, 8, 27, 9, 6, 6, 31, 37, 15, 26, 20, 43, 3, 14, 40, 20]) === "False");
    // }

    // main();











    // function solution(m, n, target, array) {
    //     let res = [], path = [];
    //     let arr = ['A', 'B', 'C', 'D', 'E'];
    //     function back(startIndex) {
    //         if (path.length === 2) {
    //             res.push([...path]);
    //             return;
    //         }
    //         for (let i = startIndex; i < arr.length; i++) {
    //             path.push({
    //                 name: arr[i],
    //                 index: i,
    //             });
    //             back(i + 1);
    //             path.pop();
    //         }
    //     }
    //     back(0);


    //     const notMatch = ['AE', 'EA', 'BD', 'DB', 'CE', 'EC', 'BE', 'EB']

    //     let map = {}

    //     for (let i = 0; i < res.length; i++) {
    //         const curGroup = res[i];
    //         let group = '';
    //         let indexA = 0, indexB = 0;
    //         curGroup.map((item, index) => {
    //             group += item.name;
    //             if (index === 0) {
    //                 indexA = item.index;
    //             } else {
    //                 indexB = item.index;
    //             }
    //         })

    //         const otherGroup = group.split('').reverse().join('')

    //         if (notMatch.indexOf(group) > -1) {
    //             map[group] = -1
    //             map[otherGroup] = -1
    //         } else {
    //             const diff = Math.abs(indexA - indexB)
    //             map[group] = diff
    //             map[otherGroup] = diff
    //         }

    //     }



    //     let minDiff = Infinity, minSolid = '';
    //     for (let i = 0; i < array.length; i++) {

    //         let diffSum = 0;
    //         const curSolid = array[i];

    //         for (let a = 0; a < curSolid.length; a++) {
    //             const curSolidStr = curSolid[a];
    //             const curStr = target[a];
    //             const group = curSolidStr + curStr;

    //             if (map[group] && map[group] > -1) {
    //                 diffSum += map[group]
    //             } else if (map[group] && map[group] === -1) {
    //                 diffSum = null;
    //                 break;
    //             } else {
    //                 diffSum += 0;
    //             }
    //         }

    //         if (diffSum && diffSum < minDiff) {
    //             minDiff = diffSum
    //             minSolid = array[i]
    //         } else if (diffSum && diffSum === minDiff) {
    //             minSolid += ` ${array[i]}`
    //         }
    //     }

    //     if (minDiff === Infinity) return 'None'
    //     return minSolid

    // }


    // function main() {
    //     // Add your test cases here
    //     const matrix = ["ACDC", "BBDC", "EBCB", "BBBB"];
    //     console.log(
    //         JSON.stringify(solution(4, 4, "AEBC", matrix)) ===
    //             JSON.stringify("None")
    //     );
    // }


    // main();




    // function solution(n, s, x) {
    //     // 直接冒泡
    //     // for (let i = 0; i < s.length; i++) {
    //     //     let isOk = true
    //     //     for (let j = 0; j < s.length - i; j++) {
    //     //         if (x[j + 1] && x[j] < x[j + 1]) {
    //     //             isOk = false
    //     //             const temp = s[j]
    //     //             s[j] = s[j + 1]
    //     //             s[j + 1] = temp

    //     //             const temp2 = x[j]
    //     //             x[j] = x[j + 1]
    //     //             x[j + 1] = temp2
    //     //         }
    //     //     }
    //     //     if (isOk) break
    //     // }
    //     // return s
    //     let map = {}
    //     for (let i = 0; i < s.length; i++) {
    //         const curPer = s[i]
    //         if (map[curPer]) {
    //             map[curPer] += x[i];
    //         } else {
    //             map[curPer] = x[i];
    //         }
    //     }

    //     let map2 = Object.keys(map).map(i => {
    //         const curV = map[i]
    //         return {
    //             person: i,
    //             hongbao: curV,
    //         }
    //     })
    //     map2.sort((a, b) => 
    //         b.hongbao - a.hongbao
    //     )

    //     return map2.map(i => i.person)
    // }

    // function main() {
    //     console.log(JSON.stringify(solution(4, ["a", "b", "c", "d"], [1, 2, 2, 1])) === JSON.stringify(['b', 'c', 'a', 'd']));
    //     console.log(JSON.stringify(solution(3, ["x", "y", "z"], [100, 200, 200])) === JSON.stringify(['y', 'z', 'x']));
    //     console.log(JSON.stringify(solution(5, ["m", "n", "o", "p", "q"], [50, 50, 30, 30, 20])) === JSON.stringify(['m', 'n', 'o', 'p', 'q']));
    // }

    // main();






    // function solution(V, W) {

    //     function back(V, W) {
    //         if (W === 0) return 'NO';
    //         let desideZhiShu = 0, desideDiff = 0;
    //         const cusNeedToPayCountCeil = Math.ceil(Math.log(W) / Math.log(V))
    //         const cusNeedToPayCountFloor = Math.floor(Math.log(W) / Math.log(V))
    //         const diffCeil = Math.pow(V, cusNeedToPayCountCeil) - W
    //         const diffFloor = Math.pow(V, cusNeedToPayCountFloor) - W

    //         if (Math.abs(diffCeil) < Math.abs(diffFloor)) {
    //             desideZhiShu = cusNeedToPayCountCeil;
    //             desideDiff = diffCeil;
    //         } else {
    //             desideZhiShu = cusNeedToPayCountFloor;
    //             desideDiff = diffFloor;
    //         }

    //         console.log(desideDiff, desideZhiShu)

    //         if (desideDiff > 0) {
    //             const shopNeedToGiveBackZhiShu = Math.log(desideDiff) / Math.log(V)
    //             console.log(shopNeedToGiveBackZhiShu)
    //             if ((shopNeedToGiveBackZhiShu + '').split('').indexOf('.') === -1) {
    //                 return "Yes"
    //             } else {
    //                 return "No"
    //             }
    //         } else {
    //             return back(V, Math.abs(desideDiff))
    //         }
    //     }

    //     return back(V, W)

    // }

    // function main() {
    //     // Add your test cases here
    //     console.log(solution(10, 9) === "Yes");
    //     console.log(solution(200, 40199) === "Yes");
    //     console.log(solution(108, 50) === "NO");
    // }
    // main();





    // function heapSort(arr) {
    //     const len = arr.length
    //     for (let i = Math.floor(len / 2) - 1; i >= 0; i--) {
    //         heapify(arr, len, i)
    //     }
    //     for (let i = len - 1; i >= 0; i--) {
    //         [arr[i], arr[0]] = [arr[0], arr[i]]
    //         heapify(arr, i, 0)
    //     }
    //     return arr
    // }

    // function heapify(arr, len, index) {
    //     let maxFather = index
    //     let leftChild = index * 2 + 1
    //     let rightChild = index * 2 + 2
    //     if (arr[leftChild] > arr[maxFather] && leftChild < len) {
    //         maxFather = leftChild
    //     }
    //     if (arr[rightChild] > arr[maxFather] && rightChild < len) {
    //         maxFather = rightChild
    //     }
    //     if (maxFather !== index) {
    //         [arr[maxFather], arr[index]] = [arr[index], arr[maxFather]]
    //         heapify(arr, len, maxFather)
    //     }
    // }

    // // function promiseAll(promises) {
    // //     return new Promise((resolve, reject) => {
    // //         if (!Array.isArray(promises)) return new Error('not array')
    // //         let result = []
    // //         let resCount = 0
    // //         for (let i = 0; i < promises.length; i++) {
    // //             Promise.resolve(promises[i]).then(res => {
    // //                 resCount++
    // //                 result[i] = res
    // //                 if (resCount === promises.length) {
    // //                     return resolve(result)
    // //                 }
    // //             }).catch(err => {
    // //                 return reject(err)
    // //             })
    // //         }
    // //     })
    // // }




    
    // function fastSort(arr, left, right) {
    //     if (left >= right) return
    //     const baseIndex = findBaseIndex(arr, left, right)

    //     fastSort(arr, left, baseIndex - 1)
    //     fastSort(arr, baseIndex + 1, right)
    // }

    // function findBaseIndex(arr, left, right) {
    //     let base = arr[left]
    //     let leftPoint = left, rightPoint = right
    //     while(leftPoint < rightPoint && leftPoint < arr.length && rightPoint >= 0) {
    //         while(arr[rightPoint] >= base && leftPoint < rightPoint) {
    //             rightPoint--
    //         }
    //         arr[leftPoint] = arr[rightPoint]
    //         while(arr[leftPoint] <= base && leftPoint < rightPoint) {
    //             leftPoint++
    //         }
    //         arr[rightPoint] = arr[leftPoint]
    //     }
    //     arr[leftPoint] = base
    //     return leftPoint
    // }

    // // fastSort(arr, 0, arr.length - 1)





    // function insertSort(arr) {
    //     for (let i = 0; i < arr.length; i++) {
    //         let curNum = arr[i];
    //         let j = i - 1;
    //         while(j >= 0 && arr[j] > curNum) {
    //             arr[j + 1] = arr[j];
    //             j--;
    //         }
    //         arr[j + 1] = curNum;
    //     }
    // }




    // function mergeSort(arr) {
    //     let len = arr.length;
    //     if (len <= 1) return arr;
    //     const mid = Math.floor(len / 2);
    //     const rightArr = arr.slice(mid);
    //     const leftArr = arr.slice(0, mid);
    //     const r = mergeSort(rightArr);
    //     const l = mergeSort(leftArr);
    //     return merge(r, l)
    // }


    // function merge(right, left) {
    //     let p1 = 0, p2 = 0;
    //     let temp = [];
    //     while(p1 < right.length && p2 < left.length) {
    //         if (right[p1] < left[p2]) {
    //             temp.push(right[p1]);
    //             p1++;
    //         } else {
    //             temp.push(left[p2]);
    //             p2++;
    //         }
    //     }
    //     if (p1 < right.length) {
    //         temp.push(...right.slice(p1))
    //     }
    //     if (p2 < left.length) {
    //         temp.push(...left.slice(p2))
    //     }
    //     return temp
    // }


    // function insertSort(arr) {
    //     for (let i = 0; i < arr.length; i++) {
    //         let curNum = arr[i];
    //         let j = i - 1;
    //         while(j >= 0 && arr[j] > curNum) {
    //             arr[j + 1] = arr[j];
    //             j--;
    //         }
    //         arr[j + 1] = curNum;
    //     }
    // }




    // function mergeSort(arr) {
    //     let len = arr.length;
    //     if (len <= 1) return arr;
    //     const mid = Math.floor(len / 2);
    //     const rightArr = arr.slice(mid);
    //     const leftArr = arr.slice(0, mid);
    //     const r = mergeSort(rightArr);
    //     const l = mergeSort(leftArr);
    //     return merge(r, l)
    // }

    







    // class Parent {
    //     constructor() {
    //         this.a = '22';
    //         this.parent = 'parent';
    //         this._x = undefined
    //     }
    //     parentThis() {
    //         console.log('parent this', this)
    //         return this
    //     }
    // }

    // class Child extends Parent {
    //     constructor() {
    //         super()
    //         this.a = '33';
    //         this.child = 'child'
    //     }
    //     childThis() {
    //         console.log('child this', this)
    //         return this
    //     }
    //     change() {
    //         this._x = [1,2]
    //     }
    // }

    // const c = new Child()
    // const this1 = c.parentThis()
    // const this2 = c.childThis()
    // console.log(this1 === this2)




    // function a() {
    //     function b() {
    //         var bb = 234;
    //         aa = 0;
    //         console.log(aa)
    //     }
    //     var aa = 123;
    //     b();
    //     return b;
    // }
    // var global = 100;
    // var demo = a();
    // demo();


    // let array = [1, 2, [3, 4, [5, 6], 7, 8], 9, 10, [11, 12]]

    // function flatten(array) {
    //     if (!Array.isArray(array)) return array;
    //     let layerArray = [];
    //     array.forEach((item, index) => {
    //         if (Array.isArray(item)) {
    //             const innerArray = flatten(item);
    //             layerArray.push(...innerArray)
    //         } else {
    //             layerArray.push(item);
    //         }
    //     })
    //     return layerArray;
    // }


    // console.log(flatten(array))





    // function solution(n, A, B, array_a) {
    //     let count = 0;
    //     let sumAll = array_a.reduce((prevItem, curItem, index) => {
    //         return prevItem + curItem
    //     }, 0)
    //     sumAll = sumAll % 10
    //     if (sumAll === A || sumAll === B) {
    //         count++
    //     }

    //     array_a = array_a.map((item, index) => item + '-' + index)
    //     let res = [];
    //     let path = [];
    //     function back(startIndex) {
    //         res.push([...path])
    //         for (let i = startIndex; i < array_a.length; i++) {
    //             path.push(array_a[i])
    //             back(i + 1)
    //             path.pop()
    //         }
    //     }
    //     back(0)

    //     function findAnotherPart(arr, arrA) {
    //         return arr.filter((item) => arrA.indexOf(item) === -1)
    //     }

    //     for (let i = 0; i < res.length; i++) {
    //         let arrA = res[i]
    //         let arrB = findAnotherPart(array_a, arrA)
    //         // console.log(arrB)
    //         let sumA = arrA.reduce((prevItem, curItem, index) => {
    //             let realNum = +(curItem.split('-')[0])
    //             return prevItem + realNum
    //         }, 0)
    //         let sumB = arrB.reduce((prevItem, curItem, index) => {
    //             let realNum = +(curItem.split('-')[0])
    //             return prevItem + realNum
    //         }, 0)
    //         // console.log(sumA, sumB)
    //         if (sumA !== 0 && sumB !== 0) {
    //             sumA = sumA % 10
    //             sumB = sumB % 10
    //             if (sumA === A && sumB === B) {
    //                 count++
    //             }
    //         }
    //     }
    //     // console.log(count)
    //     return count
    // }

    function solution(n, A, B, array_a) {
        let res = 0;
        function dg(index, sum_a, sum_b) {
            if (index === n) {
                if (sum_a % 10 === A && sum_b % 10 === B) {
                    res++;
                }
                return;
            }
            dg(index + 1, sum_a + array_a[index], sum_b);
            dg(index + 1, sum_a, sum_b + array_a[index]);
        }

        dg(0, 0, 0);

        const sum = array_a.reduce((acc, curr) => acc + curr, 0);
        if (sum % 10 === A) res++;
        if (sum % 10 === B) res++;

        return res;
    }


    // function main() {
    // //  You can add more test cases here
    // // console.log(solution(3, 1, 2, [1, 1, 1]) === 3);
    // // console.log(solution(3, 3, 5, [1, 1, 1]) === 1);
    // // console.log(solution(2, 1, 1, [1, 1]) === 2);
    // console.log(solution(5, 3, 7, [2, 3, 5, 7, 9]) === 0);
    // }

    // main();


    
    function solution(n, data) {
        let index = 0;
        let firstPoint = null
        let curPoint = null
        let lastPoint = null
        data.split('').forEach((item) => {
            let newObj = {
                value: item,
                next: null,
                last: null,
            }
            if (index === 0) {
                curPoint = firstPoint = newObj
            } else {
                curPoint.next = newObj;
                newObj.last = curPoint;
                curPoint = newObj;
                if (index === data.length - 1) {
                    firstPoint.last = newObj
                    newObj.next = firstPoint
                    lastPoint = newObj
                }
            }
            index++
        })
        curPoint = firstPoint;
        let day = 1;
        let modelER = 0;

        function changClothes() {
            console.log('--------------------------')
            let needChangeCount = 0;
            let isNeedChangeAllClothes = false;

            do {
                if (curPoint.last.value === curPoint.next.value && curPoint.value === curPoint.last.value) {
                    curPoint.isNeedChange = true
                    isNeedChangeAllClothes = true
                    needChangeCount++;
                }
                if (curPoint.value !== curPoint.next.value && curPoint.value !== curPoint.last.value) {
                    modelER++
                }
                console.log(curPoint)
                curPoint = curPoint.next
            } while (curPoint && curPoint !== firstPoint)
            if (needChangeCount === data.length) {
                return [-1, -1];
            }
            do {
                if (curPoint.isNeedChange === true) {
                    curPoint.value = curPoint.value === '1' ? '0' : '1'
                    delete curPoint.isNeedChange
                }
                curPoint = curPoint.next
            } while (curPoint && curPoint !== firstPoint)
            if (isNeedChangeAllClothes) {
                day++;
                modelER = 0;
                changClothes()
            }
        }
        const res = changClothes()
        if (res) {
            return res
        } else {
            return [day, modelER]
        }
        console.log(day)
    }


    // function main() {
    //     // Add your test cases here
    //     console.log(solution(4, "0000").toString() === [-1, -1].toString());
    //     console.log(solution(4, "1110").toString() === [2, 4].toString());
    //     console.log(solution(6, "11011").toString() === [2, 3].toString());
    // }

    // main();



    function solution(n, l, r, a) {
        let count = 0;
        for (let i = 0; i < a.length; i++) {
            if (a[i] % 2 === 0 && l <= a[i] && r >= a[i]) {
                count++
            }
        }
        return count;
    }

    // function main() {
    //     console.log(solution(5, 3, 8, [1, 2, 6, 8, 7]) === 2);
    //     console.log(solution(4, 10, 20, [12, 15, 18, 9]) === 2);
    //     console.log(solution(3, 1, 10, [2, 4, 6]) === 3);
    // }

    // main();



    function solution(plates, n) {
        let arr = [];
        let index = 0;

        while (index < plates.length) {
            let saveIndex = index;
            let startIndex = null;

            if (plates[saveIndex + 1] === plates[saveIndex] + 1) {
                while(plates[saveIndex + 1] === plates[saveIndex] + 1) {
                    if (startIndex === null) {
                        startIndex = saveIndex
                    }
                    console.log('saveIndex value', plates[saveIndex])
                    saveIndex++
                }

                if (saveIndex - startIndex >= 2) {
                    arr.push(`${plates[startIndex]}-${plates[saveIndex]}`)
                } else {
                    arr.push(...plates.slice(startIndex, saveIndex + 1))
                }
                startIndex = null;
                index = saveIndex + 1

            } else {
                arr.push(plates[index])
                index++
            }
        }
        console.log(arr)
        return arr.join(',');
    }

    // function main() {
    // //  You can add more test cases here
    // // console.log(solution([-3, -2, -1, 2, 10, 15, 16, 18, 19, 20], 10) === "-3--1,2,10,15,16,18-20");
    // console.log(solution([-6, -3, -2, -1, 0, 1, 3, 4, 5, 7, 8, 9, 10, 11, 14, 15, 17, 18, 19, 20], 20) === "-6,-3-1,3-5,7-11,14,15,17-20");
    // // console.log(solution([1, 2, 7, 8, 9, 10, 11, 19], 8) === "1,2,7-11,19");
    // }

    // main();


    function solution(n, m, k, a, b) {
        let small = Infinity;
        for (let i = 0; i < a.length; i++) {
            for (let j = 0; j < b.length; j++) {
                let curNum = Math.abs((a[i] - b[j]) ** 2 - k ** 2)
                if (curNum < small) {
                    small = curNum
                }
            }
        }
        return small;
    }

    // function main() {
    //     // You can add more test cases here
    //     console.log(solution(5, 5, 1, [5, 3, 4, 1, 2], [0, 6, 7, 9, 8]) === 0);
    //     console.log(solution(5, 5, 0, [5, 3, 4, 1, 2], [0, 6, 7, 9, 8]) === 1);
    //     console.log(solution(5, 6, 3, [5, 3, 4, 1, 2], [0, 6, 7, 9, 8, 11]) === 0);
    // }

    // main();






    function solution(num, data) {
        data = data.split('')
        let solidArr = [...data]
        // debugger
        function back() {
            let index = 0;
            let needRePos = false
            data = [...solidArr]
            while (index < data.length) {
                let curPos = data[index]
                if (curPos === 'R') {
                    if (data[index + 1] && data[index + 1] !== 'L' && data?.[index + 2] !== 'L') {
                        solidArr[index + 1] = 'R'
                    }
                }
                if (curPos === 'L') {
                    if (data[index - 1] && data[index - 1] !== 'R' && data?.[index - 2] !== 'R') {
                        solidArr[index - 1] = 'L'
                    }
                }
                index++
            }
            for (let i = 0; i < solidArr.length; i++) {
                let curPos = solidArr[i]
                let lastPos = solidArr[i - 1]
                let nextPos = solidArr[i + 1]
                if (curPos === '.') {
                    if ((lastPos && lastPos === 'R' && nextPos !== 'L') || (nextPos && nextPos === 'L' && lastPos !== 'R')) {
                        needRePos = true
                        break
                    }
                }
            }
            if (needRePos) {
                back()
            }
        }
        back()

        let posArr = []
        const res = solidArr.reduce((prev, cur, index) => {
            let num = prev
            if (cur === '.') {
                num =  prev + 1
                posArr.push(index+1)
            }
            return num
        }, 0)

        if (res !== 0) {
            return `${res}:${posArr.join(',')}`;
        } else {
            return '0'
        }
    }



    // function main() {
    //     //  You can add more test cases here
    //     console.log(solution(14, ".L.R...LR..L..") === "4:3,6,13,14");
    //     // console.log(solution(5, "R....") === "0");
    //     // console.log(solution(1, ".") === "1:1");
    //     console.log(solution(14, "RR...R...L.L..") === "3:8,13,14");
    // }

    // main();





    function solution(version1, version2) {
        function comVersion(short, long) {
            let diff = (long.length - short.length) / 2;
            for (let i = 0; i < diff; i++) {
                short = short + '.0'
            }
            return short
        }
        if (version1.length < version2.length) {
            version1 = comVersion(version1, version2)
        } else {
            version2 = comVersion(version2, version1)
        }
        console.log(version1, version2)

        version1 = version1.split('.')
        version2 = version2.split('.')

        for (let i = 0; i < version1.length; i++) {
            if (version1[i] < version2[i]) {
                return -1
            } else if (version1[i] > version2[i]) {
                return 1
            }
        }
        return 0;
    }

    // function main() {
    //     // Add your test cases here
    //     console.log(solution("0.1","1.1") === -1);
    //     console.log(solution("1.0.1","1") === 1);
    //     console.log(solution("7.5.2.4","7.5.3") === -1);
    //     console.log(solution("1.0","1.0.0") === 0);
    // }

    // main();


    // 目标
    // 16+10=26
    // 7+4=11
    // 2+1=3
    


    // 一个查看任务并发数量的方法，笨方法，应该还有一个更好的，之前在万诺coding发现的
    // tag： 任务 并发 时间 同时 进行
    function solution(n,array) {
        let taskIn = []
        let taskOut = []
        let curRuningTask = []
        for (let i = 0; i < array.length; i++) {
            let curTask = array[i]
            taskIn.push(curTask[0])
            taskOut.push(curTask[0] + curTask[1])
        }
        let longestTime = ([...taskIn, ...taskOut].sort((a, b) => b - a))[0]
        
        let maxTaskCount = -Infinity;
        for (let i = 1; i <= longestTime; i++) {
            // 进队列
            for (let j = 0; j < taskIn.length; j++) {
                if (taskIn[j] === i) {
                    curRuningTask.push('1')
                }
            }
            for (let j = 0; j < taskOut.length; j++) {
                if (taskOut[j] === i) {
                    curRuningTask.pop()
                }
            }
            if (curRuningTask.length > maxTaskCount) {
                maxTaskCount = curRuningTask.length
            }
            console.log(curRuningTask)
        }
        return maxTaskCount;
    }

    // function main() {
    //     // Add your test cases here
    //     console.log(solution(2, [[1,2],[2,3]]) === 2);
    //     console.log(solution(4,[[1,2], [2,3], [3,5], [4,3]])=== 3);
    // }

    // main();




    function solution(n, arr) {
        let bigArr = [0, 1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024]
        let index = 0;
        let max = 0;
        let outStartIndex = 0;
        let outEndIndex = 0;
        while(index < arr.length) {
            // console.log('-----------', index)
            let innerIndex = index
            let startIndex = null

            while(innerIndex < arr.length) {
                let res = arr.slice(index, innerIndex + 1).reduce((prev, cur, index) => {
                    return prev * cur
                }, 1)
                // console.log(res)
                if (res > max) {
                    max = res;
                    outStartIndex = index;
                    outEndIndex = innerIndex;
                } else if (res === max) {
                    if (index < outStartIndex) {
                        outStartIndex = index;
                        outEndIndex = innerIndex;
                    } else if (index === outStartIndex) {
                        if (innerIndex < outEndIndex) {
                            outStartIndex = index;
                            outEndIndex = innerIndex;
                        }
                    }
                }
                innerIndex++;
            }
            index++;
        }
        return [outStartIndex + 1, outEndIndex + 1];
    }

    // function main() {
    //     // Add your test cases here
    //     console.log(solution(5, [1, 2, 4, 0, 8]).toString() === [1, 3].toString());
    //     console.log(solution(7, [1, 2, 4, 8, 0, 256, 0]).toString() === [6, 6].toString());
    //     console.log(solution(8, [1, 2, 4, 8, 0, 256, 512, 0]).toString() === [6, 7].toString());
    //     console.log(solution(11, [1024,0,2,1,0,2,128,512,4,256,256]).toString() === [6, 11].toString());
    // }

    // main();







    function solution(A, B) {
        let res = B - A
        return `${res}/${B}`;
    }

    // function main() {
    //     console.log(solution(2, 7) === "5/7");
    //     console.log(solution(1, 3) === "2/3");
    //     console.log(solution(3, 5) === "2/5");
    // }

    // main();






    function solution(num) {
        let map = {}
        for (let i = 0; i < 26; i++) {
            map[i] = String.fromCharCode(97 + i)
        }
        // console.log(map)

        let path = (num + '')
        let final = []
        // debugger
        function back(index, path) {
            if (index >= path.length) {
                final.push(path)
                return
            }

            // 找一个数字
            let newArr = path.split('')
            newArr[index] = map[path[index]]
            back(index + 1, newArr.join(''))

            if (+(path[index] + path[index + 1]) <= 0 || +(path[index]) === 0) {
                return
            }
            
            if (path[index] + path[index + 1] <= 25) {
                // 找两个数字
                let newArr = path.split('')
                newArr[index] = map[path[index] + path[index + 1]]
                newArr.splice(index + 1, 1)
                back(index + 1, newArr.join(''))
            }
        }
        back(0, path)
        // console.log(path)
        return final.length;
    }

    // function main() {
    //     // You can add more test cases here
    //     console.log(solution(12258) === 5);
    //     console.log(solution(1023) === 4);
    //     console.log(solution(1400112) === 6);
    //     console.log(solution(2110101) === 10);
    // }

    // main();





    function solution(number, heroes) {
        heroes.sort((a, b) => a - b)
        console.log(heroes)
        let a = [...heroes];
        let b = [...heroes];
        let win = 0
        for (let i = 0; i < a.length; i++) {
            let findBigger = false
            for (let j = 0; j < b.length; j++) {
                if (b[j] > a[i]) {
                    findBigger = true
                    win++
                    b.splice(j, 1)
                    break
                }
            }
            if (findBigger) {
                a.splice(i, 1)
                i--
            }
        }
        console.log(win)
        return win;
    }

    // function main() {
    //     //  You can add more test cases here
    //     // console.log(solution(7, [10,1,1,1,5,5,3]) === 4);
    //     // console.log(solution(5, [1,1,1,1,1]) === 0);
    //     // console.log(solution(10, [1,2,3,4,5,6,7,8,9,10]) === 9);
    //     console.log(solution(6, [9,4,7,3,2,6]) === 6);
    // }

    // main();



    
    function solution(n, array) {
        // debugger
        let isAtEnd = false
        function back(index = 0, curEne = array[0]) {
            if (index >= array.length) {
                return
            }
            if (index === array.length - 1) {
                isAtEnd = true
                return
            }
            for (let i = 1; i <= curEne; i++) {
                let nextPos = index + i;
                if (nextPos === array.length - 1) {
                    isAtEnd = true
                    return
                }
                let nextPosEne = array[nextPos];
                if (nextPosEne === 0) {
                    continue
                }
                let nextAllEne = curEne - i + nextPosEne;
                back(nextPos, nextAllEne)
            }
        }
        back()

        if (isAtEnd) {
            return 'TRUE'
        } else {
            return 'FALSE'
        }
    }

    // function main() {
    //     // Add your test cases here
    //     // console.log(solution(5, [2, 3, 1, 1, 4]) === "TRUE");
    //     // console.log(solution(5, [3, 2, 1, 0, 4]) === "FALSE");
    //     console.log(solution(6, [1, 2, 3, 4, 0, 0]) === "TRUE");
    // }

    // main();




    function solution(a, n, monsters) {
        monsters = monsters.sort((a, b) => a - b)
        // console.log(monsters)
        let money = 0;
        let maxMoney = 0;
        // debugger
        for (let i = 0; i < monsters.length; i++) {
            // console.log('------', i)
            if (a >= monsters[i]) {
                money++;
                if (money > maxMoney) {
                    maxMoney = money;
                }
                continue;
            } else {
                let curMonster = monsters[i]
                let diff = curMonster - a;
                if (diff <= money) {
                    a = diff + a;
                    if (a >= monsters[i]) {
                        money = money - diff + 1;
                        if (money > maxMoney) {
                            maxMoney = money;
                        }
                        continue
                    }
                } else {
                    break
                }
            }
        }
        // console.log(maxMoney)
        return maxMoney;
    }

    // function main() {
    //     console.log(solution(1, 3, [2, 2, 1]) === 2);
    //     console.log(solution(3, 4, [5, 1, 3, 2]) === 3);
    //     console.log(solution(4, 5, [3, 4, 5, 1, 2]) === 4);
    //     console.log(solution(6, 17, [3,5,2,5,1,3,16,7,13,12,14,11,5,4,8,8,4]) === 10);
    // }

    // main();









    

    function solution(redpacks) {

        let max = 0
        function findMax(num) {
            if (num > max) {
                max = num
            }
        }
        function sum(arr) {
            return arr.reduce((prev, cur) => {
                return prev + cur
            }, 0)
        }


        for (let i = 0; i <= redpacks.length - 3; i++) {
            // console.log('--------i', i)
            for (let j = i; j <= redpacks.length - 2; j++) {
                // console.log('---------j', j)
                let first = redpacks.slice(0, i + 1)
                let mid = redpacks.slice(i + 1, j + 1)
                let right = redpacks.slice(j + 1)
                first = sum(first)
                mid = sum(mid)
                right = sum(right)
                // if (first === mid || first === right || right === mid) {
                //     if (first === mid) {
                //         findMax(first)
                //     } else if (first === right) {
                //         findMax(right)
                //     } else if (right === mid) {
                //         findMax(mid)
                //     }
                // }
                if (first === right) {
                    findMax(first)
                }
                // console.log('first', first)
                // console.log('mid', mid)
                // console.log('right', right)
            }
        }

        return max;
    }

    // function main() {
    //     // You can add more test cases here
    //     console.log(solution([1, 3, 4, 6, 7, 14]) === 14);
    //     console.log(solution([10,10,10,10]) === 20);
    //     console.log(solution([10000]) === 0);
    //     console.log(solution([52, 13, 61, 64, 42, 26, 4, 27, 25]) === 52);
    //     console.log(solution([2, 5, 50, 30, 60, 52, 26, 5, 74, 83, 34, 96, 6, 88, 94, 80, 64, 22, 97, 47, 46, 25, 24, 43, 76, 24, 2, 42, 51, 96, 97, 87, 47, 93, 11, 98, 41, 54, 18, 16, 11, 96, 34, 36, 87, 24, 32, 27, 62, 72, 54, 14, 67, 5, 21, 20, 44, 55, 3, 82, 19, 45, 1, 52, 14, 44, 46, 39, 83, 27, 30, 87, 61, 56, 59, 10, 83, 80, 42, 44, 75, 39, 43, 41, 23, 93, 73, 50, 94, 94, 82, 46, 87, 60, 94, 47, 52, 67, 22, 50, 49, 8, 9, 30, 62, 87, 13, 11]) === 2627);
    // }

    // main();




    function solution(n, x, y) {
        x.sort((a, b) => b - a)
        y.sort((a, b) => a - b)
        let winy = 0

        for (let i = 0; i < y.length; i++) {
            let index = 0
            while (index < x.length && x[index] >= y[i]) {
                index++
            }
            if (index < x.length && x[index]) {
                winy++
                x.splice(index, 1)
            }
        }
        return winy;
    }

    // function main() {
    //     console.log(solution(3, [1, 2, 3], [1, 2, 3]) === 2);
    //     console.log(solution(3, [1, 3, 2], [5, 5, 5]) === 3);
    //     console.log(solution(4, [2, 4, 6, 8], [3, 3, 7, 9]) === 3);
    // }

    // main();






    function solution(s) {
        let nums = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0]
        s = s.split('').map(Number)
        let count = 0
        for (let i = 0; i < s.length; i++) {
            // console.log('-----------', i)
            if (s[i + 1] !== undefined) {
                if (s[i] === s[i + 1]) {
                    // console.log('s', s)
                    let a = nums.indexOf(s[i])
                    if (a !== -1) {
                        nums.splice(a, 1)
                    }
                    let b = null
                    if (s[i + 2] !== undefined) {
                        b = nums.indexOf(s[i + 2])
                    }
                    // console.log('s[i + 2]', s[i + 2])
                    // console.log('b', b)
                    if (b !== null && b !== -1) {
                        nums.splice(b, 1)
                    }
                    // console.log('nums', nums)
                    s[i + 1] = nums[0]
                    nums = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0]
                    count++
                }
            }
        }
        return count
    }

    // function main() {
    //     console.log(solution("9923001859221") === 3);
    //     // console.log(solution("111222333") === 3);
    //     // console.log(solution("11551111") === 4);
    //     // console.log(solution("1234567890") === 0);
    // }

    // main();










    function solution(n, m, strings) {

        let map = {}
        for (let i = 0; i < 26; i++) {
            map[String.fromCharCode(97 + i)] = i
        }
        // console.log('map', map)

        let recorded = []
        let maxArr = new Set()
        for (let i = 0; i < strings.length; i++) {
            let curTang = strings[i]
            let curTianDu = 0

            let sortedTang = curTang.split('').sort().join('')
            // console.log(sortedTang)

            for (let j = 0; j < m; j++) {
                let curString = curTang[j]
                curTianDu += map[curString]
            }
            // console.log('curTianDu', curTianDu)
            maxArr.add(curTianDu)

            if (recorded.indexOf(sortedTang) === -1) {
                recorded.push(sortedTang)
            } else {
                maxArr.delete(curTianDu)
                continue
            }

        }

        if (maxArr.size === 0) {
            return 0
        } else {
            return Math.max(...maxArr);
        }

    }

    // function main() {
    //     console.log(solution(3, 3, ["ccz", "cba", "zcc"]) === 3);
    //     console.log(solution(2, 3, ["abc", "cba"]) === 0);
    //     console.log(solution(5, 2, ["aa", "bb", "ab", "ba", "cc"]) === 4);
    // }

    // main();








    function solution(n, a) {
        let map = {}
        let arr = Array(n).fill(0).map((item, index) => {
            map[index + 1] = 0
            return index + 1
        })
        for (let i = 0; i < a.length; i++) {
            for (let j = i; j < a.length; j++) {
                let child = a.slice(i, j + 1)
                child = new Set(child)
                let num = child.size
                if (arr.indexOf(num) !== -1) {
                    map[num] += 1
                }
            }
        }
        return Object.values(map)
    }

    // function main() {
    //     console.log(JSON.stringify(solution(4, [1, 2, 2, 3])) === JSON.stringify([5, 4, 1, 0]));
    //     console.log(JSON.stringify(solution(3, [1, 1, 1])) === JSON.stringify([6, 0, 0]));
    //     console.log(JSON.stringify(solution(5, [1, 2, 3, 2, 1])) === JSON.stringify([5, 5, 5, 0, 0]));
    // }

    // main();







    // function solution(s) {
    //     let map = {
    //         '<': '>',
    //         '(': ')',
    //         '[': ']',
    //         '{': '}',
    //     }
    //     let stack = [];
    //     let index = 0;
    //     let res = 0;
    //     let stillUse = {}
    //     while (index < s.length) {
    //         // console.log(s[index])
    //         let curString = s[index]
    //         if (Object.keys(map).indexOf(curString) !== -1) {
    //             stack.push(curString)
    //         } else {
    //             let needToMatched = stack.pop()
    //             if (needToMatched && map[needToMatched] !== curString) {
    //                 res ++;
    //             }
    //             if (!needToMatched) {
    //                 if (Object.keys(stillUse).length === 0) {
    //                     stillUse[needToMatched] = index;
    //                 } else {
                        
    //                     res++
    //                 }

    //             }
    //         }

    //         index++
    //     }
    //     return res;
    // }


    function solution(s) {
        // debugger

        function isMatch(left, right) {
            // 判断左括号和右括号是否匹配
            if ((left === '<' && right === '>') ||
                (left === '(' && right === ')') ||
                (left === '[' && right === ']') ||
                (left === '{' && right === '}')) {
                return true;
            }
            return false;
        }

        let n = s.length;
        let dp = Array.from({ length: n }, () => Array(n).fill(0));

        // dp[i][j] 表示子串 s[i...j] 最少修改次数
        
        // 初始化：长度为1的子串需要1次修改
        for (let i = 0; i < n; i++) {
            dp[i][i] = 1;
        }

        // 从长度为2的子串开始，逐步扩展到整个字符串
        for (let len = 2; len <= n; len++) {
            for (let i = 0; i <= n - len; i++) {
                let j = i + len - 1; // 子串的结束位置
                // 默认情况下，先假设将 s[i...j] 修改为匹配的括号串需要1次修改
                dp[i][j] = dp[i][j - 1] + 1;
                
                // 检查是否存在能够匹配的括号对
                for (let k = i; k < j; k++) {
                    if (isMatch(s[k], s[j])) {
                        dp[i][j] = Math.min(dp[i][j], dp[i][k - 1] + dp[k + 1][j - 1]);
                    }
                }
            }
        }

        // 返回整个字符串从 0 到 n-1 匹配的最小修改次数
        return dp[0][n - 1];

    }

    // function main() {
    //     // console.log(solution("[(]}") === 2);
    //     // console.log(solution("<{[]}>") === 0);
    //     // console.log(solution("<>>{(})]") === 3);
    //     console.log(solution("][>)><<<") === 5);
    // }

    // main();





    function solution(n, a) {
        // debugger
        let path = [];
        let res = [];
        let needPause = false;
        function back(startIndex) {
            if (path.length === 3) {
                path.sort((a, b) => a - b)
                if ((path[0] < path[1] && path[1] < path[2]) && (path[1] % path[0] === 0) && (path[2] % path[1] === 0)) {
                    res = [...path];
                    needPause = true;
                }
                return
            }
            for (let i = startIndex; i < a.length; i++) {
                if (!needPause) {
                    path.push(a[i])
                    back(i + 1)
                    path.pop()
                }
            }
        }
        let count = 0
        while (a.length >= 3) {
            res = []
            needPause = false
            path = []
            // console.log(a)
            back(0)
            if (res.length > 0) {
                count++
                for (let i = 0; i < res.length; i++) {
                    let pos = a.indexOf(res[i])
                    a.splice(pos, 1)
                }
            } else {
                break
            }
        }
        // console.log(res)
        return count;
    }

    // function main() {
    //     console.log(solution(7, [1, 1, 2, 3, 4, 5, 6]) === 2);
    //     console.log(solution(8, [1, 1, 2, 2, 3, 4, 5, 6]) === 2);
    //     // console.log(solution(5, [1, 2, 2, 4, 6]) === 1);
    //     // console.log(solution(6, [4,1,3,5,5,6]) === 1);
    //     // console.log(solution(6, [3,6,3,6,1,5]) === 1);
    // }

    // main();









    function solution(n, k, a) {
        a.sort((a, b) => b - a)
        let num1 = null;
        let num2 = null;
        let i = 0;
        let res = 0
        while (i < a.length) {
            let findNum = false
            num1 = a[i]
            if (a[i + 1] !== undefined) {
                if (a[i] - a[i + 1] <= k) {
                    findNum = true
                    num2 = a[i + 1]
                    if (num1 !== null && num2 !== null) {
                        res += num1 * num2
                    }
                    a.splice(i, 1)
                    a.splice(i, 1)
                    i = 0
                }
            }
            if (!findNum) {
                i++;
            }
        }
        return res;
    }

    // function main() {
    //     console.log(solution(6, 2, [1, 1, 4, 5, 1, 4]) === 21);
    //     console.log(solution(4, 1, [3, 3, 4, 4]) === 25);
    //     console.log(solution(5, 0, [2, 2, 2, 2, 2]) === 8);
    // }

    // main();







    function solution(s) {
        // debugger
        let index = 0;
        let yuan = ["a", "e", "i", "o", "u"];
        let needToStop = false;
        s = s.split('')
        let fast = 0, slow = 0;
        while(fast < s.length) {
            if (yuan.indexOf(s[fast]) === -1) {
                let mid = s[slow]
                s[slow] = s[fast]
                s[fast] = mid
                slow++
            }
            fast++;
        }
        // console.log(s)
        while (index < s.length) {
            if (s[index - 1] !== undefined) {
                if (yuan.indexOf(s[index - 1]) === -1 && yuan.indexOf(s[index]) === -1) {
                    let curI = index
                    let didFindYuan = false
                    while (curI < s.length) {
                        let curString = s[curI]
                        if (yuan.indexOf(curString) !== -1) {
                            didFindYuan = true
                            s[curI] = s[index]
                            s[index] = curString
                            break
                        }
                        curI++
                    }
                    if (!didFindYuan) {
                        // index就是最末尾的位置
                        needToStop = true
                        break
                    }
                }
            }
            index ++;
        }

        return index;
    }

    // function main() {
    //     console.log(solution("aaatra") === 6);
    //     console.log(solution("tqqa") === 3);
    //     console.log(solution("aeiou") === 5);
    //     console.log(solution("aqtqttqqt") === 3);
    // }

    // main();






        
    function solution(coins, amount) {
        let res = []
        let sum = 0
        let index = 0
        let needToMoveNext = false
        let res2 = []
        coins.sort((a, b) => b - a)
        while (index < coins.length) {
            let curNum = coins[index];
            res.push(curNum);
            sum += curNum;
            if (sum > amount) {
                res.pop()
                sum -= curNum
                needToMoveNext = true
                index++;
            } else if (sum === amount) {
                break
            }
        }
        coins.forEach((item, index) => {
            if (amount % item === 0 && item !== 1) {
                let num = amount / item
                res2 = Array(num).fill(item)
            }
        })

        if (res.length < res2.length) {
            return res
        } else {
            return res2
        }

    }

    // function main() {
    //     // Add your test cases here
    //     console.log(JSON.stringify(solution([1, 2, 5], 18)) === JSON.stringify([5, 5, 5, 2, 1]));
    //     console.log(JSON.stringify(solution([1, 3, 4], 6)) === JSON.stringify([3, 3]));
    //     console.log(JSON.stringify(solution([5], 10)) === JSON.stringify([5, 5]));
    // }

    // main();









    function solution(N, p) {
        let sum = 0
        for (let i = 0; i < p.length; i++) {
            let curI = i - 1;
            let curCount = 0;
            while (curI >= 0) {
                if (p[curI] <= p[i]) {
                    curCount = p[curI]
                    break
                }
                curI--
            }
            if (curCount) {
                sum += curCount
            }
        }
        return sum;
    }

    // function main() {
    //     console.log(solution(5, [9, 4, 5, 2, 4]) === 6);
    //     console.log(solution(4, [1, 2, 3, 5]) === 6);
    //     console.log(solution(4, [4, 3, 2, 1]) === 0);
    // }

    // main();




    function solution(n) {
        if (n === 1) {
            return 1
        }
        if (n === 0) {
            return 0
        }
        const arr = Array(n + 1).fill(0).map(i => [0, 1])
        // console.log(arr)
        arr[0][0] = 0
        arr[0][1] = 0
        arr[1][0] = 1
        arr[1][1] = 0
        arr[2][0] = 1
        arr[2][1] = 1
        for (let i = 3; i < arr.length; i++) {
            arr[i][0] = arr[i - 1][0] + arr[i - 1][1]
            arr[i][1] = arr[i - 2][0]
        }
        // console.log(arr)
        return arr[n][0] + arr[n][1];
    }

    // function main() {
    //     // Add your test cases here
    //     console.log(solution(4) === 4);
    // }

    // main();












    function solution(n, k, a) {
        let lenOfArr = n - k;
        let path = [];
        let res = [];
        let count = 0;
        function back(startIndex) {
            if (path.length === lenOfArr) {
                let newPath = [...path].sort((a, b) => a - b)
                let isMeetNeed = true
                newPath.forEach((item, index) => {
                    if (newPath[index - 1] !== undefined && item % newPath[index - 1] !== 0) {
                        isMeetNeed = false
                    }
                })
                if (isMeetNeed) {
                    count++
                }
                res.push(newPath);
                return
            }
            for (let i = startIndex; i < a.length; i++) {
                path.push(a[i]);
                back(i + 1);
                path.pop()
            }
        }
        back(0)
        // console.log(res)
        // console.log('count', count)
        return count;
    }

    // function main() {
    //     console.log(solution(6, 4, [1, 4, 2, 3, 6, 7]) === 8);
    //     console.log(solution(5, 2, [2, 4, 8, 16, 32]) === 10);
    //     console.log(solution(4, 1, [3, 6, 9, 12]) === 1);
    // }

    // main();




    function solution(n) {
        n = n.split('').sort((a, b) => b - a)
        let isOne = true
        for (let i = 0; i < n.length; i++) {
            if (n[i] !== '0' && n[i] !== '1') {
                isOne = false
            }
        }
        if (isOne) {
            return 1
        } else {
            return +(n[0])
        }
    }

    // function main() {
    //     console.log(solution("10101") === 1);
    //     console.log(solution("212") === 2);
    //     console.log(solution("1000000") === 1);
    //     console.log(solution("123456789") === 9);
    //     console.log(solution("9876543210") === 9);
    // }

    // main();






    function solution(n, s) {
        let newString = '';
        let map = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0']
        for (let i = 0; i < s.length; i++) {
            let cur = s[i];
            // 找到了字母以及后面的数字
            if (map.indexOf(cur) === -1 && map.indexOf(s[i + 1]) !== -1) {
                let curCount = 0;
                let index = i + 1;
                while (index < s.length && map.indexOf(s[index]) !== -1) {
                    index++
                }
                curCount = +(s.substring(i + 1, index))
                // console.log('curCount', curCount)
                for (let j = 0; j < curCount; j++) {
                    newString += s[i]
                }
            } else if (map.indexOf(cur) === -1 && (map.indexOf(s[i + 1]) === -1 || s[i + 1] === undefined)) {
                newString += cur
            }
            // console.log('newString', newString)
        }
        return newString; // Placeholder
    }

    // function main() {
    //     console.log(solution(9, "a2b3cde3f") === "aabbbcdeeef");
    //     console.log(solution(10, "x44yz5mn51") === "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxyzzzzzmnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn");
    //     console.log(solution(6, "adbc2d") === "adbccd");
    // }

    // main();





    function solution(n, a) {
        if (n === 1) {
            return a[0]
        }
        let max = 0;
        let maxIndex = 0;
        a.forEach((item, index) => {
            if (a[index - 1] !== undefined) {
                let mul = a[index] * a[index - 1]
                if (mul > max) {
                    maxIndex = index
                    max = mul
                }
            }
        })
        // console.log(max, '--', maxIndex)
        let res = a.reduce((prev, curItem, curIndex) => {
            if (curIndex === maxIndex - 1) {
                let mul = a[curIndex + 1] * curItem
                return prev += mul
            } else if (curIndex === maxIndex) {
                return prev
            } else {
                return prev += curItem
            }
        }, 0)
        // console.log(res)
        return res;
    }

    // function main() {
    //     console.log(solution(6, [1, 1, 4, 5, 1, 4]) === 27);
    //     console.log(solution(3, [2, 3, 5]) === 17);
    //     console.log(solution(17, [13,11,13,14,3,14,2,16,2,17,8,5,4,15,11,9,12]) === 324);
    // }

    // main();




    function solution(n, a) {
        // debugger
        let min = Infinity
        function findMin(a) {
            if (a < min) {
                min = a
            }
        }
        function back(yi, er, i) {
            if (i >= n) {
                return
            }
            if (i === n - 1) {
                let sum = yi + er
                findMin(sum)
                return
            }
            back(yi + a[i + 1], er, i + 1)
            back(yi, er + a[i + 2], i + 2)
        }
        back(a[0], 0, 0)
        console.log(min)
        return min;
    }

    // function main() {
    //     // console.log(solution(5, [0, 3, 2, 1, 0]) === 2);
    //     // console.log(solution(4, [0, 5, 6, 0]) === 5);
    //     // console.log(solution(6, [0, 1, 2, 3, 1, 0]) === 3);
    //     // console.log(solution(2, [2, 4]) === 6);
    //     console.log(solution(15, [8,6,10,13,2,6,8,11,15,8,10,4,11,16,6]) === 66);
    // }

    // main();

    





    function solution(staples, drinks, x) {
        let choices = 0;
        for (let i = 0; i < staples.length; i++) {
            let food = staples[i];
            if (food <= x) {
                choices++
            }
            for (let j = 0; j < drinks.length; j++)  {
                let drink = drinks[j];
                let sum = food + drink;
                if (sum <= x) {
                    choices++
                }
            }
        }
        drinks.forEach((item, index) => {
            if (item <= x) {
                choices++
            }
        })
        return choices; // Placeholder
    }

    // function main() {
    //     console.log(solution([5, 20, 5], [5, 5, 2], 5) === 5);
    //     console.log(solution([3, 10, 7], [2, 5], 7) === 5);
    //     console.log(solution([10, 15], [5, 6], 20) === 7);
    // }

    // main();



    function solution(n, m, t, a, b) {
        // a买入价格
        debugger
        let max = 0
        for (let i = 0; i < t.length; i++) {
            let time = t[i]
            let diff = b[i] - a[i]
            let num = Math.floor(m / time)
            let count = num * diff
            if (count > max) {
                max = count
            }
        }
        return max;
    }

    // function main() {
    //     // console.log(solution(3, 10, [3, 4, 5], [2, 3, 5], [3, 5, 7]) === 4);
    //     // console.log(solution(2, 8, [2, 3], [3, 4], [5, 7]) === 8);
    //     // console.log(solution(4, 12, [1, 2, 3, 4], [2, 3, 4, 5], [4, 5, 6, 8]) === 24);
    //     console.log(solution(13, 12, [7,2,5,5,11,13,6,1,13,17,9,15,2], [2,15,14,9,4,5,16,6,12,2,12,10,17], [12,12,7,15,3,10,15,1,2,5,9,3,8]) === 16);
    // }

    // main();






    // function solution(n, k, positions) {
    //     let mid
    //     let left = positions[0]
    //     let right = positions[positions.length - 1]
    //     let index = 1;
    //     while(left < right && index <= k - 2) {
    //         mid = (left + right) / 2
    //         left = mid
    //         index++;
    //     }
    //     console.log('left', left)
    //     return 0;
    // }

    // function main() {
    //     let positions1 = [1, 3, 4, 7, 9];
    //     let positions2 = [2, 5, 8, 11, 14, 20];
    //     let positions3 = [1, 2, 4, 8, 10, 12, 14];

    //     console.log(solution(5, 3, positions1) === 3);
    //     console.log(solution(6, 4, positions2) === 6);
    //     console.log(solution(7, 4, positions3) === 3);
    // }

    // main();


    // function solution(inputArray) {
    //     // debugger
    //     inputArray.sort((a, b) => a[0] - b[0])
    //     inputArray.forEach((item) => {
    //         item.sort((a, b) => a - b)
    //     })
    //     // console.log(inputArray)
    //     for (let index = 0; index < inputArray.length; index++) {
    //         let last = inputArray[index - 1]
    //         let item = inputArray[index]
    //         let a = []
    //         if (last && (last[0] > item[1] || item[0] > last[1])) {
    //             continue
    //         }
    //         // 第一种情况：a 小于 x
    //         if (last && last[0] >= item[0]) {
    //             if (last[1] <= item[1]) {
    //                 // console.log(last[0])
    //                 a = [item[0], item[1]]
    //                 // console.log('a', a)
    //                 inputArray[index] = a
    //                 inputArray.splice(index - 1, 1)
    //                 index--
    //             } else if (last[1] > item[1]) {
    //                 // console.log(last[0])
    //                 a = [item[0], last[1]]
    //                 // console.log('a', a)
    //                 inputArray[index] = a
    //                 inputArray.splice(index - 1, 1)
    //                 index--
    //             }
    //             // 第二种情况：a 大于 x
    //         } else if (last && last[0] < item[0]) {
    //             // 第一，y小于b
    //             if (last[1] <= item[1]) {
    //                 // console.log(last[0])
    //                 a = [last[0], item[1]]
    //                 // console.log('a', a)
    //                 inputArray[index] = a
    //                 inputArray.splice(index - 1, 1)
    //                 index--
    //                 // 第二，y大于b
    //             } else if (last[1] > item[1]) {
    //                 // console.log(last[0])
    //                 a = [last[0], last[1]]
    //                 // console.log('a', a)
    //                 inputArray[index] = a
    //                 inputArray.splice(index - 1, 1)
    //                 index--
    //             }
    //         }
    //     }
    //     let sum = 0
    //     inputArray.forEach((item) => {
    //         for (let j = item[0]; j <= item[1]; j++) {
    //             sum++
    //         }
    //     })
    //     // console.log(inputArray)
    //     return sum;
    // }

    // function main() {
    //     //  You can add more test cases here
    //     // const testArray1 = [[1,4], [7, 10], [3, 5]];
    //     // const testArray2 = [[1,2], [6, 10], [11, 15]];
    //     // const testArray3 = [[6,18],[2,16],[12,16],[5,16],[8,10],[1,9],[7,21],[2,3],[7,21],[6,7],[1,24],[9,17],[1,4],[12,18],[2,17],[4,19],[9,22],[8,24],[13,21],[7,8],[19,22],[22,23],[6,14]];
    //     const testArray4 = [[9,12],[12,23],[10,12],[8,18],[8,12],[1,4],[15,22],[4,7],[22,25],[8,11],[12,23],[10,25],[20,22],[9,22]];

    //     // console.log(solution(testArray1) === 9);
    //     // console.log(solution(testArray2) === 12);
    //     // console.log(solution(testArray3) === 24);
    //     console.log(solution(testArray4) === 25);
    // }

    // main();








    function solution(n, a, b) {
        let diff = a.map((item, index) => {
            return item - b[index]
        })
        console.log('diff', diff)
        let res = []
        let layer = []
        function back(startIndex) {
            if (layer.length > 0) {
                res.push([...layer])
            }
            for (let i = startIndex; i < diff.length; i++) {
                layer.push(diff[i])
                back(i + 1)
                layer.pop()
            }
        }
        back(0)
        let max = 0
        for (let i = 0; i < res.length; i++) {
            let cur = res[i];
            let sum = cur.reduce((prev, item, index) => {
                return prev + item
            }, 0)
            console.log('sum', sum)
            if (Math.abs(sum) > max) {
                max = Math.abs(sum)
            }
        }
        console.log('max', max)
        console.log('res', res)
        return max;
    }

    function solution(n, a, b) {
        let diff = a.map((item, index) => item - b[index]);

        let max = 0;

        let dp = new Set();
        dp.add(0);

        for (let i = 0; i < n; i++) {
            let current = Array.from(dp);
            for (let value of current) {
                dp.add(value + diff[i]);
            }
        }

        for (let value of dp) {
            max = Math.max(max, Math.abs(value));
        }

        return max;
    }

    // function main() {
    //     console.log(solution(4, [4, 2, 1, 1], [2, 1, 4, 4]) === 6);
    //     console.log(solution(3, [10, 20, 30], [15, 25, 5]) === 25);
    //     console.log(solution(5, [1, 5, 3, 7, 9], [2, 6, 4, 8, 10]) === 5);
    // }

    // main();



    // function solution(n, m, k, x) {
    //     const MOD = BigInt(10**9 + 7);
    //     x.sort((a, b) => a - b);
    //     if (m === 0) return 1;
    //     if (n < m) return 0;

    //     // Precompute factorial and inverse factorial using BigInt
    //     const maxFact = n;
    //     const fact = new Array(maxFact + 1).fill(BigInt(1));
    //     for (let i = 1; i <= maxFact; i++) {
    //         fact[i] = (fact[i - 1] * BigInt(i)) % MOD;
    //     }

    //     const invFact = new Array(maxFact + 1).fill(BigInt(1));
    //     invFact[maxFact] = powMod(fact[maxFact], MOD - BigInt(2), MOD);
    //     for (let i = maxFact - 1; i >= 0; i--) {
    //         invFact[i] = (invFact[i + 1] * BigInt(i + 1)) % MOD;
    //     }

    //     let result = BigInt(0);

    //     for (let i = 0; i < n; i++) {
    //         const leftVal = x[i];
    //         let left = i;
    //         let right = n - 1;
    //         let j = i;
    //         while (left <= right) {
    //             const mid = Math.floor((left + right) / 2);
    //             if (x[mid] <= leftVal + k) {
    //                 j = mid;
    //                 left = mid + 1;
    //             } else {
    //                 right = mid - 1;
    //             }
    //         }
    //         const count = j - i + 1;
    //         if (count >= m) {
    //             const c = comb(count - 1, m - 1, fact, invFact, MOD);
    //             result = (result + c) % MOD;
    //         }
    //     }

    //     return Number(result % MOD);
    // }

    // function powMod(base, exp, mod) {
    //     let result = BigInt(1);
    //     base = base % mod;
    //     while (exp > 0) {
    //         if (exp % BigInt(2) === BigInt(1)) {
    //             result = (result * base) % mod;
    //         }
    //         base = (base * base) % mod;
    //         exp = exp / BigInt(2);
    //     }
    //     return result;
    // }

    // function comb(n, k, fact, invFact, mod) {
    //     if (k < 0 || k > n) return BigInt(0);
    //     return (fact[n] * invFact[k] % mod) * invFact[n - k] % mod;
    // }




    function solution(n, m, k, x) {
        // 首先保证数组是从小排序的
        x.sort((a, b) => a - b);
        let count = 0
        // 遍历这个数组，拿到每一个数的最长窗口
        for (let i = 0; i < x.length; i++) {
            let left = i
            let right = i
            while(right < n && x[right] - x[left] <= k) {
                right++
            }
            console.log('right', right)
            let trueRight = right - 1
            // 开始算组合数
            let countInArray = trueRight - left + 1
            if (countInArray >= m) {
                const r = comb(countInArray - 1, m - 1)
                count += r
            }
        }
        return count
    }

    // 阶乘的公式
    function factorial(x) {
        let res = 1;
        for (let i = 2; i <= x; i++) {
            res *= i
        }
        return res
    }

    // 组合的公式：C(n, k) = n!/(k!*(n−k)!）
    // 意思是从n个数里面选k个数有多少种组合（不看顺序）
    function comb(n, k) {
        return factorial(n) / (factorial(k) * factorial(n - k))
    }

    

    // function main() {
    //     console.log(solution(4, 2, 3, [1, 2, 3, 4]) === 6);
    //     console.log(solution(4, 2, 2, [1, 2, 3, 4]) === 5);
    //     console.log(solution(5, 3, 5, [1, 3, 6, 7, 9]) === 3);
    // }

    // main();


    // function solution(t1, t2, t3) {
    //     // 将时间转换为分钟数
    //     function timeToMinutes(time) {
    //         const [hour, minute] = time.split(":").map(Number);
    //         return hour * 60 + minute;
    //     }

    //     // 将 t2 和 t3 转换为分钟数
    //     const t2Minutes = timeToMinutes(t2);
    //     const t3Minutes = timeToMinutes(t3);

    //     // 如果 t3 超过 t2，则认为超时
    //     if (t3Minutes > t2Minutes) {
    //         return 'Yes';
    //     } else {
    //         return 'No';
    //     }
    // }

    // function main() {
    //     console.log(solution("18:00", "19:05", "19:05") === "No");
    //     console.log(solution("23:00", "00:21", "00:23") === "Yes");
    //     console.log(solution("23:05", "00:05", "23:58") === "No");
    // }

    // main();







    function solution(fruits) {
        // debugger
        let max = 0
        for (let i = 0; i < fruits.length; i++) {
            console.log('-------------')
            let left = i;
            let right = i;
            let map = {}
            while(right < fruits.length && Object.keys(map).length <= 2){
                let curFruit = fruits[right]
                if (!map[fruits]) {
                    map[curFruit] = 1
                    if (Object.keys(map).length > 2) break
                } else {
                    map[curFruit] += 1
                }
                right++
            }
            console.log('right', right)
            let trueRight = right - 1
            console.log('trueRight', trueRight)
            let len = trueRight - left + 1
            console.log('len', len)
            max = Math.max(max, len)

        }
        return max;
    }

    // function main() {
    //     console.log(solution([1, 2, 1, 2]) === 4);
    //     console.log(solution([2, 0, 1, 2, 2]) === 3);
    //     // console.log(solution([1, 2, 3, 2, 2, 4]) === 4);
    // }

    // main();


    function solution(n, k, people) {
        let i = 0;
        let count = 0;
        let res = 0
        while (i < n) {
            if (count === k) {
                break
            }
            res++
            if (people[i] === 'A') {
                count++
            }
            if (i === n - 1 && count < k) {
                i = 0
            } else {
                i++
            }
        }
        console.log('res', res)
        return res;
    }

    // function main() {
    //     console.log(solution(3, 3, "AAB") === 4);
    //     console.log(solution(3, 3, "BBA") === 9);
    //     console.log(solution(5, 4, "ABABA") === 6);
    // }

    // main();







    function solution(a, b) {
        let map = Array(a.length).fill(0).map((item, index) => {
            return Array(a[index]).fill(0)
        })
        map.forEach((item, index) => {
            item[b[index] - 1] = 1
        })
        console.log(map)
        let count = map[0].indexOf(1)
        let curPos = count
        for (let i = 0; i < map.length; i++) {
            let curTree = map[i]
            let nextTree = map[i + 1]
            if (nextTree) {
                let nextAppleIndex = nextTree.indexOf(1)
                if (curPos <= nextTree.length - 1) {
                    let needToWalk = Math.abs(curPos - nextAppleIndex)+ 1
                    count += needToWalk
                } else {
                    let nextTreeTop = nextTree.length - 1
                    let needToWalk = Math.abs(curPos - nextTreeTop) + (nextTreeTop - nextAppleIndex) + 1
                    // console.log('Math.abs(curPos - nextTreeTop)', Math.abs(curPos - nextTreeTop))
                    // console.log('(nextTreeTop - nextAppleIndex)', (nextTreeTop - nextAppleIndex))
                    count += needToWalk
                }
                curPos = nextAppleIndex
            }

        }
        console.log(count + 1)
        return count + 1;
    }

    // function main() {
    //     // console.log(solution([3, 4, 5, 6, 7], [1, 2, 3, 4, 5]) === 9);
    //     // console.log(solution([4, 4, 4], [1, 2, 3]) === 5);
    //     // console.log(solution([7, 8, 6, 5, 7, 9], [2, 3, 5, 4, 6, 7]) === 14);
    //     console.log(solution([8,3], [4,3]) === 6);
    // }

    // main();








    function solution(n, m) {
        if ((n % 2 === 0 && m % 2 === 0) || (n % 2 !== 0 && m % 2 !== 0)) {
            return 'No'
        }
        return "Yes"; // placeholder return
    }

    // function main() {
    //     console.log(solution(1, 1) === "No");
    //     console.log(solution(1, 4) === "Yes");
    //     console.log(solution(4, 1) === "Yes");
    //     console.log(solution(4, 4) === "No");
    // }

    // main();






    function solution(x, y) {
        // 找子矩形！！！
        let count = 0
        function zhengCal(zheng_size) {
            // 正菱形
            // let zheng_size = 1
            for (let i = zheng_size; i <= x; i = i + zheng_size) {
                for (let j = zheng_size; j <= y; j = j + zheng_size) {
                    if (j > y - zheng_size || i > x - zheng_size) {
                        continue
                    }
                    count++
                }
            }
        }
        function shuCal(shu_x_size, shu_y_size) {
            // 竖扁菱形
            // let shu_x_size = 1, shu_y_size = 2
            if (shu_y_size > y + 1 || shu_x_size > x + 1) {
                return
            }
            for (let i = shu_x_size; i <= x; i = i + shu_x_size) {
                for (let j = shu_y_size; j <= y; j = j + shu_y_size) {
                    if (j > y + 1 || i > x + 1) {
                        break
                    }
                    if (j > y - shu_y_size || i > x - shu_x_size) {
                        continue
                    }
                    count++
                }
            }
        }
        function hengCal(heng_x_size, heng_y_size) {
            // 横扁菱形
            // let heng_x_size = 2, heng_y_size = 1
            if (heng_y_size > y + 1 || heng_x_size > x + 1) {
                return
            }
            for (let i = heng_x_size; i <= x; i = i + heng_x_size) {
                for (let j = heng_y_size; j <= y; j = j + heng_y_size) {
                    if (j > y + 1 || i > x + 1) {
                        break
                    }
                    if (j > y - heng_y_size || i > x - heng_x_size) {
                        continue
                    }
                    count++
                }
            }
        }
        let zheng = Math.floor(x / 2)

        for (let i = 1; i <= zheng; i++) {
            zhengCal(i)
            shuCal(i, i * 2)
            hengCal(i * 2, i)
        }
        console.log(count)
        return count;
    }



    function solution(x, y) {
        let count = 0;

        // 遍历菱形的中心点
        // 网格上每个点都遍历一下
        for (let cx = 0; cx <= x; cx++) {
            for (let cy = 0; cy <= y; cy++) {
                // 对于每个中心点，遍历对角线长度（的一半）
                // 其中一个竖向的对角线长度对应着很多种情况的横向的对角线长度

                // dx <= cx && dx <= x - cx表示中心点左右要有足够的位置给到对角线
                // 比如，dx <= cx表示中心点要在对角线的右边
                // dx <= x - cx表示中心点右边有足够长的对角线长度
                for (let dx = 1; dx <= cx && dx <= x - cx; dx++) {
                    for (let dy = 1; dy <= cy && dy <= y - cy; dy++) {
                        // 把情况再限定一下，防止最后++的时候出了界限，count也会被加1
                        if (cx - dx >= 0 && cx + dx <= x && cy - dy >= 0 && cy + dy <= y) {
                            count++;
                        }
                    }
                }
            }
        }
        return count;
    }




    // function main() {
    //     console.log(solution(2, 2) === 1);
    //     console.log(solution(3, 3) === 4);
    //     console.log(solution(4, 2) === 4);
    //     console.log(solution(4, 14) === 196);
    // }

    // main();
    





    function solution(s) {
        let map = {}
        for (let i = 0; i < s.length; i++) {
            let curString = s[i]
            if (map[curString]) {
                map[curString] += 1
            } else {
                map[curString] = 1
            }
        }
        let num = Object.values(map).sort((a, b) => b - a)[0]
        console.log(num)
        if (num <= 1 ) return 'NO'
        if (num === 2) return 'YES'
        if (num % 2 === 0) return 'NO'
        for (let i = 3; i <= Math.sqrt(num); i += 2) {
            if (num % i === 0) return 'NO'
        }
        return 'YES'
    }

    // function main() {
    //     console.log(solution("abccckjskw") === "YES");
    //     console.log(solution("qwqwtttqqttttt") === "NO");
    //     console.log(solution("aabbbccddd") === "YES");
    //     console.log(solution("xyzzzz") === "NO");
    //     console.log(solution("mmnnooo") === "YES");
    // }

    // main();


    function solution(arr1, arr2) {
        function cal(arr) {
            let res = 0
            for (let i = 0; i < arr.length; i++) {
                if (arr[i] === 1) {
                    res += Math.pow(-2, arr.length - 1 - i)
                }
            }
            return res
        }
        let sum1 = cal(arr1)
        let sum2 = cal(arr2)
        let sum = sum1 + sum2
        console.log(sum)
        // debugger
        function reverseCal(num) {
            let res = []
            while(num !== 0) {
                // 分别计算出商和余数
                // 商是说明这个数由多少个基数相加构成的
                // 余数是查看这个数是否为基数的倍数，以此说明当前的遍历次数是否需要对-2进行幂然后累加
                let shan = Math.floor(num / -2)
                let yushu = num % -2
                if (yushu < 0) {
                    // 余数为负数，一般出现在num为负数，且其是-2的奇数次幂
                    // 给余数加上余数原本应在的范围的(-2, 0)和(0, 2)的最大值2（基数的最大值绝对值），
                    // 使得任意负数的余数总归会回到正数
                    yushu += 2
                }
                // 余数增加了，那么相应的商也要变化，因为num = (-2) * 商 + 余数
                // 重新计算一下商
                shan = (num - yushu) / (-2)
                res.unshift(yushu === -0 ? 0 : yushu)
                num = shan
            }
            console.log(res)
            return res
        }

        return sum === 0 ? [0] : reverseCal(sum);
    }

    // function main() {
    //     console.log(JSON.stringify(solution([1, 1, 1, 1, 1], [1, 0, 1])) === JSON.stringify([1, 0, 0, 0, 0]));
    //     // console.log(JSON.stringify(solution([1, 0, 0], [1, 1, 1])) === JSON.stringify([1, 1, 0, 1, 1]));
    //     // console.log(JSON.stringify(solution([0], [0])) === JSON.stringify([0]));
    // }

    // main();


    function solution(N, K, A) {
        let map = {}
        A.forEach((item, index) => {
            if (map[item]) {
                map[item] += 1
            } else {
                map[item] = 1
            }
        })
        let VArr = Object.keys(map).sort((a, b) => b - a)
        let res = 0
        let find = 0
        let index = 0
        // debugger
        while (index < VArr.length) {
            let curV = VArr[index]
            console.log('curV', curV)
            if (map[curV] >= K) {
                find += 1
                res += find * curV

                map[curV] -= K
                if (map[curV] === 0) {
                    VArr.splice(index, 1)
                }
                index = 0
                console.log('VArr', VArr)
                console.log('map', map)
            } else {
                let value = Object.values(map).reduce((prev, item) => prev + item, 0)
                if (VArr.length === 0 && value === 0) {
                    break
                }
                index++
            }
        }
        if (VArr.length > 0) {
            return -1
        }
        console.log(res)
        return res;
    }

    // function main() {
    //     console.log(solution(6, 2, [6, 6, 1, 1, 1, 1]) === 11);
    //     console.log(solution(4, 2, [5, 5, 5, 5]) === 15);
    //     console.log(solution(5, 3, [2, 2, 2, 2, 2]) === -1);
    // }

    // main();



    let n = 0
    let prev = 0
    function frameTest(rafTime) {
        // console.log('rafTime', rafTime)
        console.log('jiange', rafTime - prev)
        prev = rafTime
        if (n <= 100) {
            requestAnimationFrame(frameTest)
        } else {

        }
        n++
    }
    requestAnimationFrame(frameTest)














  </script>
</body>

